import os

# Print current working directory
print(f"Current working directory: {os.getcwd()}")

# List files in current directory
print("\nFiles in current directory:")
for file in os.listdir():
    print(f"- {file}")

# Check if config files exist
config_py_exists = os.path.exists('config.py')
client_json_exists = os.path.exists('schwab_client.json')

print(f"\nconfig.py exists: {config_py_exists}")
print(f"schwab_client.json exists: {client_json_exists}")

# If config.py exists, print its contents
if config_py_exists:
    print("\nContents of config.py:")
    with open('config.py', 'r') as f:
        print(f.read())