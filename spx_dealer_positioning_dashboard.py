import dash
from dash import dcc, html, Input, Output, callback_context, State
import dash_bootstrap_components as dbc
import yfinance as yf
import plotly.graph_objects as go
import plotly.express as px
import plotly.subplots as make_subplots
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import random
import requests
import scipy.stats as stats

# Initialize the Dash app
app = dash.Dash(__name__, external_stylesheets=[dbc.themes.DARKLY])
app.title = "SPX Dealer Positioning Dashboard"
server = app.server

# Cache for data to reduce API calls
data_cache = {
    'all_options': None,
    'filtered_options': None,
    'current_price': None,
    'price_data': None,
    'last_update': None
}

# Custom CSS for better styling
custom_css = {
    'backgroundColor': '#121212',
    'color': '#f8f9fa',
    'fontFamily': "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
}

# Define custom CSS for cards
custom_css = {
    'background-color': '#2c3e50',
    'border-radius': '5px',
    'margin-bottom': '15px',
    'box-shadow': '0 4px 8px 0 rgba(0, 0, 0, 0.2)'
}

# Create the app layout with tabs
app.layout = html.Div([

    # Header
    dbc.Container([
        # Add content here
    ])
        dbc.Row([
            dbc.Col(html.H1("SPX Options Analysis Dashboard", className="my-3"), width=8),
            dbc.Col([
                dbc.Row([
                    dbc.Col(html.Div(id='current-price-display', className="text-right h3")),
                ]),
                dbc.Row([
                    dbc.Col(html.Div(id='last-update-time', className="text-right text-muted"))
                ])
            ], width=4)
        ]),

        # Controls row
        dbc.Row([
            # Ticker selector
            dbc.Col([
                html.Label("Symbol:"),
                dcc.Dropdown(
                    id='ticker-selector',
                    options=[
                        {'label': 'SPX', 'value': '^SPX'},
                        {'label': 'SPY', 'value': 'SPY'},
                        {'label': 'QQQ', 'value': 'QQQ'},
                        {'label': 'IWM', 'value': 'IWM'},
                        {'label': 'DIA', 'value': 'DIA'}
                    ],
                    value='^SPX',
                    clearable=False,
                    className="mb-3"
                )
            ], md=2),
            
            # Expiration selector
            dbc.Col([
                html.Label("Expiration:"),
                dcc.Dropdown(
                    id='dte-selector',
                    options=[
                        {'label': '0 DTE (Today)', 'value': 0},
                        {'label': '1 DTE', 'value': 1},
                        {'label': '2 DTE', 'value': 2},
                        {'label': '7 DTE', 'value': 7},
                        {'label': 'All Available', 'value': -1}
                    ],
                    value=0,
                    clearable=False,
                    className="mb-3"
                )
            ], md=2),
            
            # Refresh button
            dbc.Col([
                dbc.Button('Refresh Data', id='refresh-button', color="success", className="w-100 mt-4")
            ], md=2),
            
            # Spacer
            dbc.Col(md=2),
            
            # Market status
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.H5("Market Status", className="card-title text-center"),
                        html.Div(id='market-status', className="text-center h5")
                    ])
                ])
            ], md=4)
        ], className="mb-4"),
        
        # Tabs for different analysis sections
        dbc.Tabs(
            # Tab 1: Dealer Positioning
            dbc.Tab([
                dbc.Card([
                    dbc.CardBody([
                        dcc.Graph(id='dealer-positioning-graph')
                    ])
                ], className="mt-3"),
                
                dbc.Row([
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader("Hedging Flows"),
                            dbc.CardBody([
                                dcc.Graph(id='hedging-flows')
                            ])
                        ])
                    ], md=6),
                    
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader("Charm Exposure"),
                            dbc.CardBody([
                                dcc.Graph(id='charm-exposure')
                            ])
                        ])
                    ], md=6),
                ], className="mt-3"),
                
                dbc.Card([
                    dbc.CardHeader("Gamma Exposure Heatmap"),
                    dbc.CardBody([
                        dcc.Graph(id='gamma-heatmap')
                    ])
                ], className="mt-3")
            ], label="Dealer Positioning", tab_id="tab-dealer-positioning"),
            
            # Tab 2: Options Sentiment
            dbc.Tab([
                dbc.Row([
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader("Put/Call Ratio Tracker"),
                            dbc.CardBody([
                                dcc.Graph(id='put-call-ratio-graph')
                            ])
                        ])
                    ], md=6),
                    
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader("Options-Based Fear/Greed Index"),
                            dbc.CardBody([
                                dcc.Graph(id='fear-greed-index')
                            ])
                        ])
                    ], md=6)
                ], className="mt-3"),
                
                dbc.Card([
                    dbc.CardHeader("Unusual Options Activity"),
                    dbc.CardBody([
                        dcc.Graph(id='unusual-options-activity')
                    ])
                ], className="mt-3")
            ], label="Options Sentiment", tab_id="tab-options-sentiment"),
            
            # Tab 3: Volatility Analysis
            dbc.Tab([
                dbc.Row([
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader("IV Surface"),
                            dbc.CardBody([
                                dcc.Graph(id='iv-surface')
                            ])
                        ])
                    ], md=6),
                    
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader("IV Skew Analysis"),
                            dbc.CardBody([
                                dcc.Graph(id='iv-skew')
                            ])
                        ])
                    ], md=6)
                ], className="mt-3"),
                
                dbc.Row([
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader("Term Structure Analysis"),
                            dbc.CardBody([
                                dcc.Graph(id='term-structure')
                            ])
                        ])
                    ], md=6),
                    
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader("IV Percentile"),
                            dbc.CardBody([
                                dcc.Graph(id='iv-percentile')
                            ])
                        ])
                    ], md=6)
                ], className="mt-3")
            ], label="Volatility Analysis", tab_id="tab-volatility-analysis"),
            
            # Tab 4: Options Flow
            dbc.Tab([
                dbc.Card([
                    dbc.CardHeader("Options Flow Heatmap"),
                    dbc.CardBody([
                        dcc.Graph(id='options-flow-heatmap')
                    ])
                ], className="mt-3"),
                
                dbc.Row([
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader("Smart Money Tracker"),
                            dbc.CardBody([
                                dcc.Graph(id='smart-money-tracker')
                            ])
                        ])
                    ], md=6),
                    
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader("Whale Trade Analysis"),
                            dbc.CardBody([
                                dcc.Graph(id='whale-trades')
                            ])
                        ])
                    ], md=6)
                ], className="mt-3")
            ], label="Options Flow", tab_id="tab-options-flow"),
            
            # Tab 5: Risk Analysis
            dbc.Tab([
                dbc.Row([
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader("Max Pain Analysis"),
                            dbc.CardBody([
                                dcc.Graph(id='max-pain')
                            ])
                        ])
                    ], md=6),
                    
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader("Expected Move Calculator"),
                            dbc.CardBody([
                                dcc.Graph(id='expected-move')
                            ])
                        ])
                    ], md=6)
                ], className="mt-3"),
                
                dbc.Row([
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader("Pin Risk Analysis"),
                            dbc.CardBody([
                                dcc.Graph(id='pin-risk')
                            ])
                        ])
                    ], md=6),
                    
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader("Options-Based Support/Resistance"),
                            dbc.CardBody([
                                dcc.Graph(id='options-support-resistance')
                            ])
                        ])
                    ], md=6)
                ], className="mt-3")
            ], label="Risk Analysis", tab_id="tab-risk-analysis"),
            
            # Tab 6: Trading Strategies
            dbc.Tab(
                dbc.Row([
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader("Strategy Recommender"),
                            dbc.CardBody([
                                dcc.Graph(id='strategy-recommender')
                            ])
                        ])
                    ], md=6),
                    
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader("Risk/Reward Visualizer"),
                            dbc.CardBody([
                                dcc.Graph(id='risk-reward')
                            ])
                        ])
                    ], md=6)
                ], className="mt-3"),
                
                dbc.Row([
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader("Probability Calculator"),
                            dbc.CardBody([
                                dcc.Graph(id='probability-calculator')
                            ])
                        ])
                    ], md=6),
                    
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader("Options Backtest Tool"),
                            dbc.CardBody([
                                dcc.Graph(id='options-backtest')
                            ])
                        ])
                    ], md=6)
                ], className="mt-3")
            ], label="Trading Strategies", tab_id="tab-trading-strategies"),
            
            # Tab 7: Market Mechanics
            dbc.Tab(
                dbc.Row(
                    dbc.Col(
                        dbc.Card(
                            dbc.CardHeader("Vanna Exposure"),
                            dbc.CardBody([
                                dcc.Graph(id='vanna-exposure')
                            ])
                        )
                    ),
                    
                    dbc.Col(
                        dbc.Card(
                            dbc.CardHeader("Gamma Exposure by Expiration"),
                            dbc.CardBody([
                                dcc.Graph(id='gamma-by-expiry')
                            ])
                        )
                    )
                ),
                
                dbc.Row(
                    dbc.Col(
                        dbc.Card(
                            dbc.CardHeader("Zero Gamma Level Tracker"),
                            dbc.CardBody([
                                dcc.Graph(id='zero-gamma-tracker')
                            ])
                        )
                    )
                ),
                className="mb-4"
            )
        ], id="tabs", active_tab="tab-dealer-positioning")

# Run the app
if __name__ == '__main__':
    app.run_server(debug=True)
