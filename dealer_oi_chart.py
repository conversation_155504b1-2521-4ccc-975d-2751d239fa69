import dash
from dash import dcc, html
import dash_bootstrap_components as dbc
import plotly.graph_objs as go
import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime, timedelta

def fetch_options_data(ticker='^SPX', force_refresh=False):
    """Fetch options data for the given ticker"""
    try:
        # Get ticker data
        stock = yf.Ticker(ticker)
        current_price = stock.history(period="1d")['Close'].iloc[-1]
        print(f"Current price for {ticker}: {current_price}")
        
        # Get expiration dates
        expirations = stock.options
        print(f"Available expirations: {expirations}")
        
        # Select the nearest expiration dates
        expirations_to_fetch = expirations[:5]  # Get first 5 expirations
        print(f"Fetching data for expirations: {expirations_to_fetch}")
        
        all_options = []
        for expiry in expirations_to_fetch:
            try:
                print(f"Fetching option chain for {expiry}")
                opt_chain = stock.option_chain(expiry)
                
                # Process calls
                calls = opt_chain.calls.copy()
                print(f"Call options columns: {calls.columns.tolist()}")
                calls['type'] = 'call'
                calls['expiry'] = expiry
                
                # Process puts
                puts = opt_chain.puts.copy()
                print(f"Put options columns: {puts.columns.tolist()}")
                puts['type'] = 'put'
                puts['expiry'] = expiry
                
                # Combine and append
                options = pd.concat([calls, puts])
                all_options.append(options)
                
            except Exception as e:
                print(f"Error fetching data for {expiry}: {e}")
                continue
        
        if not all_options:
            print("No options data retrieved")
            return pd.DataFrame(), [], None, 0, pd.DataFrame()
            
        options_data = pd.concat(all_options)
        
        # Print sample data for debugging
        print(f"Sample data (first 3 rows):")
        print(options_data.head(3))
        
        # Print columns for debugging
        print(f"Original columns: {options_data.columns.tolist()}")
        
        # Rename columns to match expected format
        column_mapping = {
            'contractSymbol': 'contract',
            'lastTradeDate': 'last_trade_date',
            'strike': 'strike',  # Already correct but included for completeness
            'lastPrice': 'last_price',
            'bid': 'bid',
            'ask': 'ask',
            'change': 'change',
            'percentChange': 'percent_change',
            'volume': 'volume',
            'openInterest': 'openInterest',
            'impliedVolatility': 'iv',
            'inTheMoney': 'in_the_money'
        }
        
        # Only rename columns that exist
        rename_dict = {old: new for old, new in column_mapping.items() if old in options_data.columns}
        options_data = options_data.rename(columns=rename_dict)
        
        # Print columns after renaming for debugging
        print(f"Columns after renaming: {options_data.columns.tolist()}")
        
        # If 'strike' column is missing, create it from the index
        if 'strike' not in options_data.columns:
            print("'strike' column not found, checking for alternatives...")
            
            # Check if there's a column that might contain strike prices
            potential_strike_columns = [col for col in options_data.columns if 'strike' in col.lower()]
            if potential_strike_columns:
                strike_col = potential_strike_columns[0]
                print(f"Found potential strike column: {strike_col}")
                options_data['strike'] = options_data[strike_col]
            else:
                print("No strike column found. Creating one from the contract symbol.")
                # If we can't find a strike column, we'll need to extract it from the contract symbol
                # Format is typically like: SPX230616C04300000 (SPX, date, C/P for call/put, strike with decimal)
                def extract_strike(symbol):
                    try:
                        # Extract the strike price from the option symbol
                        # This is a simplified version and may need adjustment
                        print(f"Extracting strike from symbol: {symbol}")
                        strike_part = symbol.split(ticker.replace('^', ''))[1][7:]
                        if 'C' in strike_part:
                            strike_part = strike_part.split('C')[1]
                        elif 'P' in strike_part:
                            strike_part = strike_part.split('P')[1]
                        # Convert to a float, handling the decimal point
                        strike = float(strike_part) / 1000
                        print(f"Extracted strike: {strike}")
                        return strike
                    except Exception as e:
                        print(f"Error extracting strike from {symbol}: {e}")
                        return None
                
                if 'contractSymbol' in options_data.columns:
                    print("Using 'contractSymbol' to extract strikes")
                    options_data['strike'] = options_data['contractSymbol'].apply(extract_strike)
                elif 'contract' in options_data.columns:
                    print("Using 'contract' to extract strikes")
                    options_data['strike'] = options_data['contract'].apply(extract_strike)
                else:
                    print("No contract symbol column found. Cannot extract strikes.")
                    # Create a dummy strike column based on index
                    options_data['strike'] = range(4000, 4000 + len(options_data))
                    print("Created dummy strike values")
        
        # If 'openInterest' column is missing, create it with default values
        if 'openInterest' not in options_data.columns:
            print("'openInterest' column not found, checking for alternatives...")
            
            # Check if there's a column that might contain open interest
            potential_oi_columns = [col for col in options_data.columns 
                                   if 'interest' in col.lower() or 'oi' == col.lower()]
            if potential_oi_columns:
                oi_col = potential_oi_columns[0]
                print(f"Found potential open interest column: {oi_col}")
                options_data['openInterest'] = options_data[oi_col]
            else:
                print("No open interest column found. Using volume as a proxy.")
                # If we can't find an OI column, use volume as a proxy or set to a default value
                if 'volume' in options_data.columns:
                    options_data['openInterest'] = options_data['volume']
                else:
                    options_data['openInterest'] = 100  # Default value
        
        # Calculate days to expiration
        options_data['expiry_date'] = pd.to_datetime(options_data['expiry'])
        today = pd.Timestamp.today().normalize()
        options_data['dte'] = (options_data['expiry_date'] - today).dt.days
        
        # Final check for required columns
        required_columns = ['strike', 'openInterest', 'type', 'dte']
        for col in required_columns:
            if col not in options_data.columns:
                print(f"ERROR: Required column '{col}' is still missing after processing")
        
        # Print final data structure
        print(f"Final data columns: {options_data.columns.tolist()}")
        print(f"Data shape: {options_data.shape}")
        print(f"Sample of final data (first 3 rows):")
        print(options_data.head(3))
        
        return options_data, [], expirations[0], current_price, options_data
        
    except Exception as e:
        print(f"Error fetching options data: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame(), [], None, 0, pd.DataFrame()

def plot_dealer_oi_directional(options, current_price, selected_dte=0):
    """Create a bar chart showing dealer directional exposure based on open interest"""
    if options.empty:
        print("Empty options dataframe received")
        # Return empty figure if no data
        fig = go.Figure()
        fig.update_layout(
            title=dict(
                text="Error: No options data available",
                font=dict(size=18, color="#f8f9fa")
            ),
            template="plotly_dark",
            plot_bgcolor='#1e1e1e',
            paper_bgcolor='#1e1e1e',
            font=dict(color='#f8f9fa'),
        )
        return fig
    
    # Print available columns for debugging
    print(f"Available columns in plot_dealer_oi_directional: {options.columns.tolist()}")
    print(f"Data types: {options.dtypes}")
    print(f"Data shape: {options.shape}")
    
    # Check if required columns exist
    required_columns = ['strike', 'openInterest', 'type', 'dte']
    missing_columns = [col for col in required_columns if col not in options.columns]
    
    if missing_columns:
        print(f"Missing required columns: {missing_columns}")
        # Return empty figure if required columns are missing
        fig = go.Figure()
        fig.update_layout(
            title=dict(
                text=f"Error: Missing required data columns: {', '.join(missing_columns)}",
                font=dict(size=18, color="#f8f9fa")
            ),
            template="plotly_dark",
            plot_bgcolor='#1e1e1e',
            paper_bgcolor='#1e1e1e',
            font=dict(color='#f8f9fa'),
        )
        return fig
    
    # Filter by DTE if needed
    if selected_dte >= 0:
        filtered_options = options[options['dte'] == selected_dte]
        if filtered_options.empty:
            print(f"No options found for DTE={selected_dte}")
            # If filtering results in empty data, use all data
            filtered_options = options
            print(f"Using all available options data instead")
    else:
        filtered_options = options
    
    print(f"Filtered data shape: {filtered_options.shape}")
    
    # Group by strike
    calls = filtered_options[filtered_options['type'] == 'call']
    puts = filtered_options[filtered_options['type'] == 'put']
    
    print(f"Calls count: {len(calls)}, Puts count: {len(puts)}")
    
    # Calculate dealer directional exposure (opposite of customer position)
    # Dealers are short calls and long puts
    try:
        # Create a DataFrame with all unique strikes
        all_strikes = pd.DataFrame({'strike': sorted(set(filtered_options['strike'].unique()))})
        
        # Calculate call exposure
        if not calls.empty:
            call_exposure = calls.groupby('strike')['openInterest'].sum().reset_index()
            call_exposure.columns = ['strike', 'call_exposure']
            call_exposure['call_exposure'] = call_exposure['call_exposure'] * -1  # Dealers short calls
            all_strikes = pd.merge(all_strikes, call_exposure, on='strike', how='left')
        else:
            all_strikes['call_exposure'] = 0
            
        # Calculate put exposure
        if not puts.empty:
            put_exposure = puts.groupby('strike')['openInterest'].sum().reset_index()
            put_exposure.columns = ['strike', 'put_exposure']
            all_strikes = pd.merge(all_strikes, put_exposure, on='strike', how='left')
        else:
            all_strikes['put_exposure'] = 0
            
        # Fill NaN values with 0
        all_strikes = all_strikes.fillna(0)
        
        # Calculate net exposure
        all_strikes['net_exposure'] = all_strikes['call_exposure'] + all_strikes['put_exposure']
        
        # This is our dealer exposure DataFrame
        dealer_exposure = all_strikes
        
        print(f"Dealer exposure data shape: {dealer_exposure.shape}")
        print(f"Dealer exposure columns: {dealer_exposure.columns.tolist()}")
        
        # Find top positive and negative exposures
        top_positive = dealer_exposure[dealer_exposure['net_exposure'] > 0].nlargest(3, 'net_exposure')
        top_negative = dealer_exposure[dealer_exposure['net_exposure'] < 0].nsmallest(3, 'net_exposure')
        
        # Create figure
        fig = go.Figure()
        
        # Add bars with borders
        for i, row in dealer_exposure.iterrows():
            try:
                strike = row['strike']
                exposure = row['net_exposure']
                
                # Check if this strike is in the top values
                is_top_positive = strike in top_positive['strike'].values if not top_positive.empty else False
                is_top_negative = strike in top_negative['strike'].values if not top_negative.empty else False
                
                # Determine color based on value
                if is_top_positive:
                    color = '#00bc8c'  # Bright green for top positive
                    border_width = 3
                elif is_top_negative:
                    color = '#e74c3c'  # Bright red for top negative
                    border_width = 3
                else:
                    # Regular colors for non-top values
                    color = 'rgba(0, 188, 140, 0.5)' if exposure >= 0 else 'rgba(231, 76, 60, 0.5)'
                    border_width = 1
                
                # Add a bar with border
                fig.add_trace(go.Bar(
                    x=[strike],
                    y=[exposure],
                    showlegend=False,
                    marker=dict(
                        color=color,
                        line=dict(
                            color='rgba(255, 255, 255, 0.8)',
                            width=border_width
                        )
                    ),
                    width=10,  # Adjust width to create grid-like appearance
                    text=f"{int(exposure):,}" if (is_top_positive or is_top_negative) else None,
                    textposition='auto',
                    hovertemplate='Strike: %{x}<br>OI Exposure: %{y:,.0f}<extra></extra>'
                ))
            except Exception as e:
                print(f"Error processing row {i}: {e}")
                print(f"Row data: {row}")
                continue
        
        # Add vertical line for current price
        fig.add_vline(
            x=current_price, 
            line_width=2, 
            line_dash="dash", 
            line_color="#3498db",
            annotation_text="Current Price", 
            annotation_position="top right",
            annotation=dict(font=dict(color="#3498db", size=12))
        )
        
        # Update layout
        fig.update_layout(
            title=dict(
                text=f"Dealer Directional Exposure by Strike (DTE: {selected_dte if selected_dte >= 0 else 'All'})",
                font=dict(size=18, color="#f8f9fa")
            ),
            xaxis_title=dict(text="Strike Price", font=dict(size=14, color="#f8f9fa")),
            yaxis_title=dict(text="Dealer Open Interest Exposure", font=dict(size=14, color="#f8f9fa")),
            height=650,
            template="plotly_dark",
            plot_bgcolor='#1e1e1e',
            paper_bgcolor='#1e1e1e',
            font=dict(color='#f8f9fa'),
            margin=dict(l=40, r=40, t=60, b=250),
            barmode='relative',
            bargap=0.05,
        )
        
        # Add zero line
        fig.add_hline(y=0, line_width=1, line_dash="dash", line_color="rgba(255,255,255,0.3)")
        
        return fig
        
    except Exception as e:
        print(f"Error in dealer exposure calculation: {e}")
        import traceback
        traceback.print_exc()
        
        # Return error figure
        fig = go.Figure()
        fig.update_layout(
            title=dict(
                text=f"Error: {str(e)}",
                font=dict(size=18, color="#f8f9fa")
            ),
            template="plotly_dark",
            plot_bgcolor='#1e1e1e',
            paper_bgcolor='#1e1e1e',
            font=dict(color='#f8f9fa'),
        )
        return fig

# If you want to run this as a standalone app
if __name__ == '__main__':
    app = dash.Dash(__name__, external_stylesheets=[dbc.themes.DARKLY])
    app.title = "Dealer OI Directional Exposure"
    
    app.layout = html.Div([
        dbc.Container([
            dbc.Row([
                dbc.Col(html.H1("Dealer OI Directional Exposure", className="my-4"), width=12)
            ]),
            dbc.Row([
                dbc.Col([
                    dbc.Card([
                        dbc.CardHeader("Controls"),
                        dbc.CardBody([
                            html.Label("Select Expiration:"),
                            dcc.Dropdown(
                                id='dte-selector',
                                options=[
                                    {'label': '0 DTE (Today)', 'value': 0},
                                    {'label': '1 DTE', 'value': 1},
                                    {'label': '2 DTE', 'value': 2},
                                    {'label': '7 DTE', 'value': 7},
                                    {'label': 'All Available', 'value': -1}
                                ],
                                value=0,
                                clearable=False,
                                className="mb-3"
                            ),
                            dbc.Button('Refresh Data', id='refresh-button', color="success", className="w-100"),
                            html.Div(id='last-update-time', className="mt-2 text-center text-muted small")
                        ])
                    ])
                ], md=4)
            ], className="mb-4"),
            dbc.Row([
                dbc.Col([
                    dbc.Card([
                        dbc.CardHeader("Dealer OI Directional Exposure"),
                        dbc.CardBody([
                            dcc.Graph(id='dealer-oi-graph')
                        ])
                    ])
                ], md=12)
            ]),
            dcc.Interval(id='interval-component', interval=300*1000, n_intervals=0)  # 5 minutes
        ])
    ])
    
    @app.callback(
        [dash.Output('dealer-oi-graph', 'figure'),
         dash.Output('last-update-time', 'children')],
        [dash.Input('dte-selector', 'value'),
         dash.Input('refresh-button', 'n_clicks'),
         dash.Input('interval-component', 'n_intervals')]
    )
    def update_graph(selected_dte, n_clicks, n_intervals):
        # Get the current time for display
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Determine if this is a forced refresh
        ctx = dash.callback_context
        if ctx.triggered:
            trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]
            force_refresh = trigger_id == 'refresh-button'
        else:
            force_refresh = False
        
        # Fetch data
        options_data, price_data, expiry, current_price, all_options = fetch_options_data(force_refresh=force_refresh)
        
        # Filter data based on selected DTE if applicable
        filtered_options = options_data
        if selected_dte >= 0 and not options_data.empty:
            # Get list of available expiries
            available_expiries = sorted(options_data['expiry'].unique())
            
            # Select the expiry that corresponds to the selected DTE
            if selected_dte < len(available_expiries):
                selected_expiry = available_expiries[selected_dte]
                filtered_options = options_data[options_data['expiry'] == selected_expiry]
        
        # Generate chart
        fig = plot_dealer_oi_directional(filtered_options, current_price, selected_dte)
        
        # Update the last update time display
        last_update_text = f"Last updated: {current_time}"
        
        return fig, last_update_text
    
    app.run(debug=True, port=8051)
