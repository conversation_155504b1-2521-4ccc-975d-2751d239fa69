import os
import json
from datetime import datetime, timezone
from authlib.integrations.httpx_client import OAuth2Client
import config

def run_setup():
    # Define file path for token storage
    filepath = os.path.join(os.getcwd(), 'schwab_client.json')
    print(f"Config file will be saved to: {filepath}")
    
    # Define token endpoint
    token_endpoint = 'https://api.schwabapi.com/v1/oauth/token'
    
    # Get credentials from config, ensuring no whitespace
    api_key = config.API_KEY.strip()
    app_secret = config.APP_SECRET.strip()
    callback_url = "https://127.0.0.1"  # Hardcoded clean URL
    
    print(f"Using callback URL: {callback_url}")
    
    # Create initial config
    client_config = {
        'client': {
            'api_key': api_key,
            'app_secret': app_secret,
            'callback': callback_url,
            'setup': datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        },
        'token': {}
    }
    
    try:
        # Create OAuth client with clean callback URL
        oauth = OAuth2Client(api_key, redirect_uri=callback_url)
        
        # Generate authorization URL
        authorization_url, state = oauth.create_authorization_url('https://api.schwabapi.com/v1/oauth/authorize')
        print('Click the link below:')
        print(authorization_url)
        
        # Get redirected URL from user
        redirected_url = input('Paste URL: ').strip()
        
        # Fetch token
        token = oauth.fetch_token(
            token_endpoint,
            authorization_response=redirected_url,
            client_id=api_key,
            auth=(api_key, app_secret)
        )
        
        # Save token to config
        client_config['token'] = token
        
        # Save config to file
        with open(filepath, 'w') as f:
            json.dump(client_config, f)
            
        print("Setup completed successfully!")
        return True
        
    except Exception as e:
        print(f'Setup failed: {e}')
        return False

if __name__ == "__main__":
    run_setup()