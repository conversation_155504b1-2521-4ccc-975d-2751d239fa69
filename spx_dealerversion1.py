import dash
from dash import dcc, html, Input, Output, callback_context
import dash_bootstrap_components as dbc
import yfinance as yf
import plotly.graph_objs as go
import plotly.express as px
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import random
import requests

# Initialize the app with a dark theme
app = dash.Dash(__name__, external_stylesheets=[dbc.themes.DARKLY])
app.title = "SPX Dealer Positioning Dashboard"

# Cache for data to reduce API calls
data_cache = {
    'options_data': None,
    'price_data': None,
    'expiry': None,
    'current_price': None,
    'all_options': None,
    'last_update': None
}

# Custom CSS for better styling
custom_css = {
    'backgroundColor': '#121212',
    'color': '#f8f9fa',
    'fontFamily': "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
}

def fetch_options_data(ticker='^SPX', days_to_include=5, force_refresh=False, selected_dte=0):
    # Check cache first with longer validity
    current_time = datetime.now()
    cache_valid = (data_cache['last_update'] is not None and 
                  (current_time - data_cache['last_update']).total_seconds() < 900)  # Cache valid for 15 minutes
    
    if cache_valid and not force_refresh and data_cache['options_data'] is not None:
        return (data_cache['options_data'], data_cache['price_data'], 
                data_cache['expiry'], data_cache['current_price'], 
                data_cache['all_options'])
    
    # Implement retry logic with exponential backoff
    max_retries = 5  # Increased from 3
    retry_delay = 5  # Start with 5 seconds instead of 2
    
    for attempt in range(max_retries):
        try:
            # Let yfinance handle its own session management
            data = yf.Ticker(ticker)
            
            # Get expirations with retry
            for exp_attempt in range(3):
                try:
                    expirations = data.options
                    if expirations:
                        break
                    time.sleep(2)
                except:
                    time.sleep(2)
                    continue
                    
            if not expirations:
                print("Failed to get expiration dates")
                return pd.DataFrame(), [], None, 0, pd.DataFrame()

            # Get current price for reference - try multiple methods
            try:
                current_price = data.history(period='1d')['Close'].iloc[-1]
            except:
                try:
                    current_price = data.fast_info['lastPrice']
                except:
                    try:
                        # Try to get from a different source
                        alt_data = yf.download(ticker, period='1d')
                        current_price = alt_data['Close'].iloc[-1]
                    except:
                        print("Failed to get current price")
                        return pd.DataFrame(), [], None, 0, pd.DataFrame()
            
            # Optimize: If selected_dte is specified and not -1 (all), only fetch that specific expiration
            if selected_dte >= 0 and selected_dte < len(expirations):
                # Only fetch the selected expiration
                expirations_to_fetch = [expirations[selected_dte]]
                print(f"Optimized: Only fetching data for selected DTE {selected_dte} (expiry: {expirations_to_fetch[0]})")
            else:
                # Limit to nearest expirations (for multi-expiry analysis)
                expirations_to_fetch = expirations[:min(days_to_include, len(expirations))]
                print(f"Fetching data for {len(expirations_to_fetch)} expirations")
            
            # Use a more aggressive delay between requests
            base_delay = 3.0  # Increased base delay
            
            all_options = []
            for i, expiry in enumerate(expirations_to_fetch):
                try:
                    # Add increasing delay between requests to avoid rate limiting
                    # First expiry is most important, so use shorter delay
                    if i == 0:
                        delay = base_delay
                    else:
                        delay = base_delay + (i * 2)  # Increasing delay for later expirations
                        
                    print(f"Waiting {delay:.1f}s before fetching {expiry}...")
                    time.sleep(delay)
                    
                    # Try to get option chain with multiple attempts
                    opt_chain = None
                    for chain_attempt in range(3):
                        try:
                            opt_chain = data.option_chain(expiry)
                            break
                        except:
                            time.sleep(2)
                            continue
                            
                    if opt_chain is None:
                        print(f"Failed to get option chain for {expiry}")
                        continue
                        
                    calls = opt_chain.calls.copy()
                    puts = opt_chain.puts.copy()
                    
                    # Check if 'delta' column exists, if not calculate it
                    if 'delta' not in calls.columns:
                        # Calculate approximate delta for calls
                        calls['delta'] = calls.apply(
                            lambda row: max(0, min(1, 0.5 + 0.5 * (current_price - row['strike']) / (0.1 * current_price))), 
                            axis=1
                        )
                    
                    if 'delta' not in puts.columns:
                        # Calculate approximate delta for puts
                        puts['delta'] = puts.apply(
                            lambda row: min(0, max(-1, -0.5 + 0.5 * (current_price - row['strike']) / (0.1 * current_price))), 
                            axis=1
                        )
                    
                    # Check if 'gamma' column exists, if not calculate it
                    if 'gamma' not in calls.columns:
                        # Calculate approximate gamma for calls
                        calls['gamma'] = calls.apply(
                            lambda row: max(0, 4 * (1 - abs(2 * row['delta'] - 1))), 
                            axis=1
                        )
                    
                    if 'gamma' not in puts.columns:
                        # Calculate approximate gamma for puts
                        puts['gamma'] = puts.apply(
                            lambda row: max(0, 4 * (1 - abs(2 * abs(row['delta']) - 1))), 
                            axis=1
                        )
                    
                    calls['type'] = 'call'
                    puts['type'] = 'put'
                    
                    # Add expiration date
                    calls['expiry'] = expiry
                    puts['expiry'] = expiry
                    
                    # Calculate days to expiration for charm calculation
                    exp_date = datetime.strptime(expiry, '%Y-%m-%d')
                    today = datetime.now()
                    days_to_exp = (exp_date - today).days + (exp_date - today).seconds / 86400
                    
                    calls['dte'] = days_to_exp
                    puts['dte'] = days_to_exp
                    
                    all_options.append(calls)
                    all_options.append(puts)
                    
                    # If we at least got the first expiration, we can proceed
                    if i == 0:
                        print(f"Successfully fetched first expiration: {expiry}")
                        
                except Exception as e:
                    print(f"Error fetching data for {expiry}: {e}")
                    # If this is the first expiration and it failed, it's critical
                    if i == 0:
                        raise Exception(f"Failed to fetch first expiration: {e}")
                    continue
            
            if not all_options:
                return pd.DataFrame(), [], None, 0, pd.DataFrame()
                
            options = pd.concat(all_options)
            
            # Ensure openInterest column exists
            if 'openInterest' not in options.columns:
                options['openInterest'] = options.get('openInt', options.get('volume', 0))
            
            # Calculate dealer positions (inverse of market positions)
            options['delta_exposure'] = -1 * options['openInterest'] * options['delta'] * 100
            options['gamma_exposure'] = -1 * options['openInterest'] * options['gamma'] * 100 * 100
            
            # Calculate charm (delta decay per day)
            options['charm'] = options.apply(
                lambda row: -1 * row['openInterest'] * (row['delta'] / max(1, row['dte'])) * 100,
                axis=1
            )
            
            options['strike'] = options['strike'].astype(float)
            
            # Calculate net dealer position by type
            options['dealer_delta_position'] = options.apply(
                lambda row: row['delta_exposure'] if row['type'] == 'call' else -row['delta_exposure'], 
                axis=1
            )
            
            # Focus on the first expiration for main analysis
            nearest_expiry = expirations_to_fetch[0]
            nearest_options = options[options['expiry'] == nearest_expiry]
            
            # Get price data with retry - use a longer interval to avoid rate limits
            try:
                # Try 5m data first instead of 1m to reduce API calls
                price_data = data.history(period='1d', interval='5m')
            except:
                try:
                    # If 5m fails, try 15m
                    price_data = data.history(period='1d', interval='15m')
                except:
                    # If all fails, use daily data
                    price_data = data.history(period='5d', interval='1d')
            
            # Update cache
            data_cache['options_data'] = nearest_options
            data_cache['price_data'] = price_data
            data_cache['expiry'] = nearest_expiry
            data_cache['current_price'] = current_price
            data_cache['all_options'] = options
            data_cache['last_update'] = current_time
            
            print(f"Successfully fetched data for {len(expirations_to_fetch)} expirations")
            return nearest_options, price_data, nearest_expiry, current_price, options
            
        except yf.exceptions.YFRateLimitError:
            print(f"Rate limit hit, attempt {attempt+1}/{max_retries}. Waiting {retry_delay} seconds...")
            time.sleep(retry_delay)
            retry_delay *= 2  # Exponential backoff
        except Exception as e:
            print(f"Error fetching data: {e}")
            # Return cached data if available, otherwise empty data
            if data_cache['options_data'] is not None:
                print("Returning cached data due to error")
                return (data_cache['options_data'], data_cache['price_data'], 
                        data_cache['expiry'], data_cache['current_price'], 
                        data_cache['all_options'])
            return pd.DataFrame(), [], None, 0, pd.DataFrame()
    
    # If we've exhausted all retries, return cached data if available
    if data_cache['options_data'] is not None:
        print("Returning cached data after max retries")
        return (data_cache['options_data'], data_cache['price_data'], 
                data_cache['expiry'], data_cache['current_price'], 
                data_cache['all_options'])
    
    return pd.DataFrame(), [], None, 0, pd.DataFrame()

def process_options_data(data, current_price):
    """Process options data to calculate exposures"""
    if data is None or not hasattr(data, 'calls') or not hasattr(data, 'puts'):
        return pd.DataFrame()
        
    # Process calls
    calls = data.calls.copy()
    calls['type'] = 'call'
    
    # Process puts
    puts = data.puts.copy()
    puts['type'] = 'put'
    
    # Combine options
    options = pd.concat([calls, puts])
    
    # Filter for liquidity
    options = options[options['volume'] > 0]
    
    if options.empty:
        return pd.DataFrame()
    
    # Calculate days to expiration
    options['expiry_date'] = pd.to_datetime(options['expiration'])
    today = pd.Timestamp.today().normalize()
    options['dte'] = (options['expiry_date'] - today).dt.days
    
    # Calculate basic exposures
    options['delta_exposure'] = -1 * options['openInterest'] * options['delta'] * 100  # 100 shares per contract
    options['gamma_exposure'] = -1 * options['openInterest'] * options['gamma'] * 100 * 100  # Gamma per 1% move
    
    # Calculate additional Greeks
    options = calculate_greeks(options, current_price)
    
    return options

def plot_dashboard(options, price_data, expiry, current_price, all_options, selected_dte=0):
    # Create subplot with secondary y-axis
    fig = go.Figure()

    if not options.empty:
        # Calculate cumulative delta exposure (dealer hedging needs)
        # Split by calls and puts for better visualization
        calls = options[options['type'] == 'call']
        puts = options[options['type'] == 'put']
        
        # Group by strike for bar charts
        all_deltas = pd.concat([
            calls.groupby('strike')['dealer_delta_position'].sum(),
            puts.groupby('strike')['dealer_delta_position'].sum()
        ]).groupby(level=0).sum().reset_index()
        
        # Find top 3 positive and negative delta positions
        top_positive = all_deltas[all_deltas['dealer_delta_position'] > 0].nlargest(3, 'dealer_delta_position')
        top_negative = all_deltas[all_deltas['dealer_delta_position'] < 0].nsmallest(3, 'dealer_delta_position')
        
        # Plot dealer delta exposure by strike as bar charts
        for i, row in all_deltas.iterrows():
            strike = row['strike']
            delta_value = row['dealer_delta_position']
            
            # Check if this strike is in the top values
            is_top_positive = strike in top_positive['strike'].values if not top_positive.empty else False
            is_top_negative = strike in top_negative['strike'].values if not top_negative.empty else False
            
            # Determine color based on value
            if delta_value >= 0:
                color = '#00bc8c' if is_top_positive else 'rgba(0, 188, 140, 0.5)'
            else:
                color = '#e74c3c' if is_top_negative else 'rgba(231, 76, 60, 0.5)'
                
            border_width = 2 if (is_top_positive or is_top_negative) else 1
            
            # Add a bar with border
            fig.add_trace(go.Bar(
                x=[strike],
                y=[delta_value],
                name='Dealer Delta' if i == 0 else None,  # Only add to legend once
                showlegend=(i == 0),
                marker=dict(
                    color=color,
                    line=dict(
                        color='rgba(255, 255, 255, 0.8)',
                        width=border_width
                    )
                ),
                width=10,  # Adjust width to create grid-like appearance
                text=f"{int(delta_value):,}" if (is_top_positive or is_top_negative) else None,
                textposition='auto',
                hovertemplate='Strike: %{x}<br>Delta: %{y:,.0f}<extra></extra>'
            ))
        
        # Calculate net dealer position at current price
        net_delta = options['dealer_delta_position'].sum()
        net_gamma = options['gamma_exposure'].sum()
        
        # Add SPX price as a vertical line at the current price
        fig.add_vline(
            x=current_price, 
            line_width=2, 
            line_dash="dash", 
            line_color="#3498db",
            annotation_text=f"Current Price: ${current_price:.0f}", 
            annotation_position="top right",
            annotation=dict(font=dict(color="#3498db", size=12))
        )
        
        # Add annotations for top 3 negative deltas
        positions = [0.25, 0.5, 0.75]  # Left, center, right
        
        # Add individual boxes for top negative deltas
        for i, row in enumerate(top_negative.iterrows()):
            if i >= len(positions):  # Skip if we have more than 3 items
                break
                
            idx, row_data = row
            delta_value = int(row_data['dealer_delta_position'])
            strike = row_data['strike']
            
            fig.add_annotation(
                x=positions[i],  # Use fixed position
                y=-0.32,  # Position below the title
                xref="paper",
                yref="paper",
                text=f"<span style='color:#e74c3c'>{delta_value:,}@{strike:.0f}</span>",
                showarrow=False,
                font=dict(size=10, color="#f8f9fa"),
                align="center",
                bgcolor="rgba(30, 30, 30, 0.7)",
                bordercolor="rgba(231, 76, 60, 0.5)",
                borderwidth=2,
                borderpad=4,
                opacity=0.9
            )
        
        # Add individual boxes for top positive deltas
        for i, row in enumerate(top_positive.iterrows()):
            if i >= len(positions):  # Skip if we have more than 3 items
                break
                
            idx, row_data = row
            delta_value = int(row_data['dealer_delta_position'])
            strike = row_data['strike']
            
            fig.add_annotation(
                x=positions[i],  # Use fixed position
                y=-0.49,  # Position below the title
                xref="paper",
                yref="paper",
                text=f"<span style='color:#00bc8c'>{delta_value:,}@{strike:.0f}</span>",
                showarrow=False,
                font=dict(size=10, color="#f8f9fa"),
                align="center",
                bgcolor="rgba(30, 30, 30, 0.7)",
                bordercolor="rgba(0, 188, 140, 0.5)",
                borderwidth=2,
                borderpad=4,
                opacity=0.9
            )

    # Configure axes and layout with improved styling
    fig.update_layout(
        title=dict(
            text=f"SPX Dealer Positioning (Expiry: {expiry}, DTE: {selected_dte if selected_dte >= 0 else 'All'})",
            font=dict(size=24, color="#f8f9fa")
        ),
        xaxis_title=dict(text="Strike Price", font=dict(size=14, color="#f8f9fa")),
        yaxis=dict(
            title=dict(text="Delta Exposure", font=dict(size=14, color="#f8f9fa")),
            side='left',
            gridcolor='rgba(255,255,255,0.1)'
        ),
        legend=dict(
            x=0.01,
            y=0.99,
            bgcolor='rgba(30,30,30,0.8)',
            bordercolor='rgba(255,255,255,0.2)'
        ),
        margin=dict(l=40, r=40, t=80, b=250),  # Increased bottom margin for annotations
        height=700,  # Increased height to accommodate annotations
        template="plotly_dark",
        plot_bgcolor='#1e1e1e',
        paper_bgcolor='#1e1e1e',
        font=dict(color='#f8f9fa'),
        barmode='relative',
        bargap=0.05  # Small gap between bars
    )
    
    # Add a subtitle with key metrics including current price
    fig.add_annotation(
        x=0.5,
        y=1.06,
        xref="paper",
        yref="paper",
        text=f"SPX Price: ${current_price:,.2f} | Net Delta: {net_delta:,.0f} | Net Gamma: {net_gamma:,.0f}",
        showarrow=False,
        font=dict(size=16, color="#f8f9fa"),
        align="center",
    )
    
    # Add zero line
    fig.add_hline(y=0, line_width=1, line_dash="dash", line_color="rgba(255,255,255,0.3)")
    
    return fig

def plot_hedging_flows(options, current_price, selected_dte=0):
    # Calculate expected hedging flows for different price movements
    price_moves = np.arange(-2.0, 2.1, 0.5)  # -2% to +2% in 0.5% increments
    hedging_flows = {}
    
    for move in price_moves:
        # Calculate price change in absolute terms
        price_change = current_price * (move / 100)
        new_price = current_price + price_change
        
        # Apply a convexity adjustment for larger moves (gamma of gamma effect)
        convexity_factor = 1.0 + (abs(move) * 0.1)  # Larger moves have more non-linear effects
        
        # Calculate adjusted gamma based on price move direction
        adjusted_gamma = options.apply(
            lambda row: adjust_gamma_for_price_move(row, current_price, new_price, move, convexity_factor),
            axis=1
        )
        
        # Calculate new delta after price move based on adjusted gamma
        delta_change = adjusted_gamma * price_change
        new_delta = options['dealer_delta_position'] + delta_change
        
        # The hedging flow is the difference between new and current delta positions
        hedging_flows[move] = new_delta.sum() - options['dealer_delta_position'].sum()
    
    hedging_df = pd.DataFrame({
        'price_move_pct': list(hedging_flows.keys()),
        'hedging_flow': list(hedging_flows.values())
    })
    
    # Find top 3 positive and top 3 negative flows
    top_positive = hedging_df[hedging_df['hedging_flow'] > 0].sort_values('hedging_flow', ascending=False).head(3)
    top_negative = hedging_df[hedging_df['hedging_flow'] < 0].sort_values('hedging_flow').head(3)
    
    # Create grid-like bar chart
    fig = go.Figure()
    
    # Add bars with borders
    for i, row in hedging_df.iterrows():
        move = row['price_move_pct']
        flow = row['hedging_flow']
        
        # Check if this move is in the top positive or negative flows
        is_top_positive = move in top_positive['price_move_pct'].values
        is_top_negative = move in top_negative['price_move_pct'].values
        
        # Determine color based on value
        if is_top_positive:
            color = '#00bc8c'  # Bright green for top positive
            border_width = 3
        elif is_top_negative:
            color = '#e74c3c'  # Bright red for top negative
            border_width = 3
        else:
            # Regular colors for non-top values
            color = 'rgba(0, 188, 140, 0.5)' if flow >= 0 else 'rgba(231, 76, 60, 0.5)'
            border_width = 1
        
        # Add a bar with border - no legend entries
        fig.add_trace(go.Bar(
            x=[move],
            y=[flow],
            showlegend=False,
            marker=dict(
                color=color,
                line=dict(
                    color='rgba(255, 255, 255, 0.8)',
                    width=border_width
                )
            ),
            width=0.4,  # Adjust width to create grid-like appearance
            text=f"{int(flow):,}" if (is_top_positive or is_top_negative) else None,
            textposition='auto',
            hovertemplate='Price Move: %{x}%<br>Hedging Flow: %{y:,.0f}<extra></extra>'
        ))
    
    fig.update_layout(
        title=dict(
            text=f"Expected Dealer Hedging Flows Based on Price Movement (DTE: {selected_dte if selected_dte >= 0 else 'All'})",
            font=dict(size=18, color="#f8f9fa")
        ),
        xaxis_title=dict(text="Price Movement (%)", font=dict(size=14, color="#f8f9fa")),
        yaxis_title=dict(text="Required Hedging Flow (+ Buy, - Sell)", font=dict(size=14, color="#f8f9fa")),
        height=650,  # Further increased height
        template="plotly_dark",
        plot_bgcolor='#1e1e1e',
        paper_bgcolor='#1e1e1e',
        font=dict(color='#f8f9fa'),
        margin=dict(l=40, r=40, t=60, b=250),  # Dramatically increased bottom margin
        barmode='relative',
        bargap=0.05,  # Small gap between bars
    )
    
    # Add zero line
    fig.add_hline(y=0, line_width=1, line_dash="dash", line_color="rgba(255,255,255,0.3)")
    
    # Create negative flows annotation - separate boxes for each value
    if not top_negative.empty:
        # Add title for negative flows
        fig.add_annotation(
            x=0.5,
            y=-0.25,  # Position for title
            xref="paper",
            yref="paper",
            text="<span style='color:#e74c3c'>Negative Flows:</span>",
            showarrow=False,
            font=dict(size=12, color="#f8f9fa"),
            align="center",
            bgcolor="rgba(30, 30, 30, 0.7)",
            bordercolor="rgba(255, 255, 255, 0.3)",
            borderwidth=1,
            borderpad=4,
            opacity=0.9
        )
        
        # Fixed positions for a symmetrical layout - 3 boxes
        positions = [0.25, 0.5, 0.75]  # Left, center, right
        
        # Add individual boxes for each negative flow - centered positions
        for i, row in enumerate(top_negative.iterrows()):
            if i >= len(positions):  # Skip if we have more than 3 items
                break
                
            idx, row_data = row
            flow_value = int(row_data['hedging_flow'])
            move_pct = row_data['price_move_pct']
            
            fig.add_annotation(
                x=positions[i],  # Use fixed position
                y=-0.32,  # Position below the title
                xref="paper",
                yref="paper",
                text=f"<span style='color:#e74c3c'>{flow_value:,}@{move_pct}%</span>",
                showarrow=False,
                font=dict(size=10, color="#f8f9fa"),
                align="center",
                bgcolor="rgba(30, 30, 30, 0.7)",
                bordercolor="rgba(231, 76, 60, 0.5)",
                borderwidth=2,
                borderpad=4,
                opacity=0.9
            )
    
    # Create positive flows annotation - separate boxes for each value
    if not top_positive.empty:
        # Add title for positive flows
        fig.add_annotation(
            x=0.5,
            y=-0.42,  # Position below negative flows
            xref="paper",
            yref="paper",
            text="<span style='color:#00bc8c'>Positive Flows:</span>",
            showarrow=False,
            font=dict(size=12, color="#f8f9fa"),
            align="center",
            bgcolor="rgba(30, 30, 30, 0.7)",
            bordercolor="rgba(255, 255, 255, 0.3)",
            borderwidth=1,
            borderpad=4,
            opacity=0.9
        )
        
        # Add individual boxes for each positive flow - centered positions
        for i, row in enumerate(top_positive.iterrows()):
            if i >= len(positions):  # Skip if we have more than 3 items
                break
                
            idx, row_data = row
            flow_value = int(row_data['hedging_flow'])
            move_pct = row_data['price_move_pct']
            
            fig.add_annotation(
                x=positions[i],  # Use fixed position
                y=-0.49,  # Position below the title
                xref="paper",
                yref="paper",
                text=f"<span style='color:#00bc8c'>{flow_value:,}@{move_pct}%</span>",
                showarrow=False,
                font=dict(size=10, color="#f8f9fa"),
                align="center",
                bgcolor="rgba(30, 30, 30, 0.7)",
                bordercolor="rgba(0, 188, 140, 0.5)",
                borderwidth=2,
                borderpad=4,
                opacity=0.9
            )
    
    return fig

def adjust_gamma_for_price_move(row, current_price, new_price, move_pct, convexity_factor=1.0):
    """Adjust gamma based on price move direction and magnitude"""
    # Base gamma from the data
    base_gamma = row['gamma']
    
    # For large moves, gamma changes as we move away from the strike
    # This is a simplified model of gamma dynamics
    
    strike = row['strike']
    option_type = row['type']
    
    # Calculate moneyness before and after the move
    initial_moneyness = current_price / strike
    new_moneyness = new_price / strike
    
    # Gamma decreases as we move further ITM or OTM
    # This is a simplified approximation
    if option_type == 'call':
        if move_pct > 0:  # Moving more ITM for calls
            # Gamma decreases as calls move deeper ITM
            gamma_adjustment = 1.0 - min(0.5, abs(move_pct) * 0.2) * convexity_factor
        else:  # Moving more OTM for calls
            # Gamma also decreases as calls move further OTM
            gamma_adjustment = 1.0 - min(0.7, abs(move_pct) * 0.3) * convexity_factor
    else:  # Puts
        if move_pct < 0:  # Moving more ITM for puts
            # Gamma decreases as puts move deeper ITM
            gamma_adjustment = 1.0 - min(0.5, abs(move_pct) * 0.2) * convexity_factor
        else:  # Moving more OTM for puts
            # Gamma also decreases as puts move further OTM
            gamma_adjustment = 1.0 - min(0.7, abs(move_pct) * 0.3) * convexity_factor
    
    # Apply the adjustment
    adjusted_gamma = base_gamma * gamma_adjustment
    
    return adjusted_gamma

def plot_gamma_heatmap(options, current_price, selected_dte=0):
    if options.empty:
        return go.Figure()
    
    # Filter for the selected DTE if specified
    if selected_dte >= 0:
        selected_data = options[options['dte'] == selected_dte]
        if selected_data.empty:
            selected_data = options  # Fallback to all data if no data for selected DTE
    else:
        selected_data = options
    
    # Group by strike for the selected expiry
    gamma_by_strike = selected_data.groupby('strike')['gamma_exposure'].sum().reset_index()
    
    # Find top 3 positive and top 3 negative gamma values
    top_positive = gamma_by_strike[gamma_by_strike['gamma_exposure'] > 0].sort_values('gamma_exposure', ascending=False).head(3)
    top_negative = gamma_by_strike[gamma_by_strike['gamma_exposure'] < 0].sort_values('gamma_exposure').head(3)
    
    # Create a grid-like bar chart
    fig = go.Figure()
    
    # Add bars with borders
    for i, row in gamma_by_strike.iterrows():
        strike = row['strike']
        gamma_value = row['gamma_exposure']
        
        # Check if this strike is in the top positive or negative values
        is_top_positive = strike in top_positive['strike'].values
        is_top_negative = strike in top_negative['strike'].values
        
        # Determine color based on value
        if is_top_positive:
            color = '#00bc8c'  # Bright green for top positive
            border_width = 3
        elif is_top_negative:
            color = '#e74c3c'  # Bright red for top negative
            border_width = 3
        else:
            # Regular colors for non-top values
            color = 'rgba(0, 188, 140, 0.5)' if gamma_value >= 0 else 'rgba(231, 76, 60, 0.5)'
            border_width = 1
        
        # Add a bar with border - no legend entries
        fig.add_trace(go.Bar(
            x=[strike],
            y=[gamma_value],
            showlegend=False,
            marker=dict(
                color=color,
                line=dict(
                    color='rgba(255, 255, 255, 0.8)',
                    width=border_width
                )
            ),
            width=10,  # Adjust width to create grid-like appearance
            text=f"{int(gamma_value):,}" if (is_top_positive or is_top_negative) else None,
            textposition='auto',
            hovertemplate='Strike: %{x}<br>Gamma: %{y:,.0f}<extra></extra>'
        ))
    
    # FIXED: Add vertical line for current price - ensure it's at the correct position
    fig.add_vline(
        x=current_price, 
        line_width=2, 
        line_dash="dash", 
        line_color="#3498db",
        annotation_text="Current Price", 
        annotation_position="top right",
        annotation=dict(font=dict(color="#3498db", size=12))
    )
    
    # Calculate net gamma for the selected expiry
    net_gamma = selected_data['gamma_exposure'].sum()
    
    # COMPLETELY REVISED: Find gamma flip levels with direct zero-crossing detection
    gamma_by_strike = gamma_by_strike.sort_values('strike')
    
    # Direct approach to find zero crossings
    flip_levels = []
    
    # Iterate through adjacent strikes to find sign changes
    for i in range(len(gamma_by_strike) - 1):
        gamma1 = gamma_by_strike['gamma_exposure'].iloc[i]
        gamma2 = gamma_by_strike['gamma_exposure'].iloc[i + 1]
        
        # Check if gamma crosses zero between these strikes
        if (gamma1 > 0 and gamma2 < 0) or (gamma1 < 0 and gamma2 > 0):
            strike1 = gamma_by_strike['strike'].iloc[i]
            strike2 = gamma_by_strike['strike'].iloc[i + 1]
            
            # Linear interpolation to find the exact zero crossing
            # If gamma1 and gamma2 have the same magnitude, the zero crossing is at the midpoint
            if abs(gamma1) == abs(gamma2):
                flip_point = (strike1 + strike2) / 2
            else:
                # Calculate the zero crossing using linear interpolation
                # x = x1 + (0 - y1) * (x2 - x1) / (y2 - y1)
                flip_point = strike1 + (0 - gamma1) * (strike2 - strike1) / (gamma2 - gamma1)
            
            flip_levels.append(flip_point)
    
    # If no flip levels found with direct approach, try with smoothed data
    if not flip_levels:
        # Apply smoothing to reduce noise in gamma values
        window_size = min(5, len(gamma_by_strike) // 3)  # Use smaller window for smaller datasets
        if window_size > 0:
            gamma_by_strike['smooth_gamma'] = gamma_by_strike['gamma_exposure'].rolling(
                window=window_size, center=True, min_periods=1
            ).mean()
            
            # Iterate through adjacent strikes to find sign changes in smoothed data
            for i in range(len(gamma_by_strike) - 1):
                gamma1 = gamma_by_strike['smooth_gamma'].iloc[i]
                gamma2 = gamma_by_strike['smooth_gamma'].iloc[i + 1]
                
                # Skip if either value is NaN
                if pd.isna(gamma1) or pd.isna(gamma2):
                    continue
                
                # Check if gamma crosses zero between these strikes
                if (gamma1 > 0 and gamma2 < 0) or (gamma1 < 0 and gamma2 > 0):
                    strike1 = gamma_by_strike['strike'].iloc[i]
                    strike2 = gamma_by_strike['strike'].iloc[i + 1]
                    
                    # Linear interpolation to find the exact zero crossing
                    try:
                        flip_point = strike1 + (0 - gamma1) * (strike2 - strike1) / (gamma2 - gamma1)
                        flip_levels.append(flip_point)
                    except ZeroDivisionError:
                        # If division by zero, use midpoint
                        flip_point = (strike1 + strike2) / 2
                        flip_levels.append(flip_point)
    
    # If still no flip levels, try a third approach with cumulative gamma
    if not flip_levels:
        # Calculate cumulative gamma exposure
        gamma_by_strike['cumulative_gamma'] = gamma_by_strike['gamma_exposure'].cumsum()
        
        # Find where the slope of cumulative gamma changes significantly
        for i in range(1, len(gamma_by_strike) - 1):
            slope1 = gamma_by_strike['cumulative_gamma'].iloc[i] - gamma_by_strike['cumulative_gamma'].iloc[i-1]
            slope2 = gamma_by_strike['cumulative_gamma'].iloc[i+1] - gamma_by_strike['cumulative_gamma'].iloc[i]
            
            # If slope changes significantly, it might be a gamma flip region
            if abs(slope1 - slope2) > abs(net_gamma) * 0.1:  # 10% of net gamma as threshold
                flip_point = gamma_by_strike['strike'].iloc[i]
                flip_levels.append(flip_point)
    
    # Add flip level lines with improved styling - using purple dotted lines
    for level in flip_levels:
        # Only add flip levels that are within a reasonable range of current price
        # (within ±30% of current price)
        if 0.7 * current_price <= level <= 1.3 * current_price:
            # Add vertical line for gamma flip
            fig.add_vline(
                x=level, 
                line_width=2, 
                line_dash="dot", 
                line_color="#9b59b6",  # Purple color
            )
            
            # Add a more prominent annotation box for the gamma flip
            fig.add_annotation(
                x=level,
                y=0,  # Position at the top of the chart
                text=f"Gamma Flip: {level:.0f}",
                showarrow=True,
                arrowhead=2,
                arrowsize=1,
                arrowwidth=2,
                arrowcolor="#9b59b6",
                font=dict(size=14, color="white"),
                bgcolor="#9b59b6",
                bordercolor="white",
                borderwidth=1,
                borderpad=4,
                opacity=0.8
            )
    
    # Add annotations for top positive gamma levels
    for i, row in top_positive.iterrows():
        fig.add_annotation(
            x=row['strike'],
            y=row['gamma_exposure'],
            text=f"+{int(row['gamma_exposure']):,}",
            showarrow=True,
            arrowhead=2,
            arrowsize=1,
            arrowwidth=1,
            arrowcolor="#00bc8c",
            font=dict(size=12, color="white"),
            bgcolor="#00bc8c",
            bordercolor="white",
            borderwidth=1,
            borderpad=3,
            opacity=0.8
        )
    
    # Add annotations for top negative gamma levels
    for i, row in top_negative.iterrows():
        fig.add_annotation(
            x=row['strike'],
            y=row['gamma_exposure'],
            text=f"{int(row['gamma_exposure']):,}",
            showarrow=True,
            arrowhead=2,
            arrowsize=1,
            arrowwidth=1,
            arrowcolor="#e74c3c",
            font=dict(size=12, color="white"),
            bgcolor="#e74c3c",
            bordercolor="white",
            borderwidth=1,
            borderpad=3,
            opacity=0.8
        )
    
    # Update layout with improved styling
    fig.update_layout(
        title=dict(
            text=f"Gamma Exposure by Strike (DTE: {selected_dte if selected_dte >= 0 else 'All'})",
            font=dict(size=20, color="#f8f9fa")
        ),
        xaxis_title=dict(text="Strike Price", font=dict(size=14, color="#f8f9fa")),
        yaxis_title=dict(text="Gamma Exposure", font=dict(size=14, color="#f8f9fa")),
        template="plotly_dark",
        plot_bgcolor='#1e1e1e',
        paper_bgcolor='#1e1e1e',
        font=dict(color='#f8f9fa'),
        height=500,
        margin=dict(l=40, r=40, t=80, b=150)  # Increased bottom margin for annotations
    )
    
    # Add net gamma annotation
    fig.add_annotation(
        x=0.5,
        y=-0.3,  # Position below the chart
        xref="paper",
        yref="paper",
        text=f"Net Gamma: {net_gamma:,.0f}",
        showarrow=False,
        font=dict(size=14, color="#f8f9fa"),
        align="center",
        bgcolor="rgba(30, 30, 30, 0.7)",
        bordercolor="rgba(255, 255, 255, 0.3)",
        borderwidth=1,
        borderpad=4,
        opacity=0.9
    )
    
    return fig

def plot_charm_exposure(options, current_price, selected_dte=0):
    if options.empty:
        return go.Figure()
    
    # Filter for the selected DTE if specified
    if selected_dte >= 0:
        selected_data = options[options['dte'] == selected_dte]
        if selected_data.empty:
            selected_data = options  # Fallback to all data if no data for selected DTE
    else:
        selected_data = options
    
    # Group by strike for charm exposure
    charm_by_strike = selected_data.groupby('strike')['charm'].sum().reset_index()
    
    # Find top 3 positive and top 3 negative charm values
    top_positive = charm_by_strike[charm_by_strike['charm'] > 0].sort_values('charm', ascending=False).head(3)
    top_negative = charm_by_strike[charm_by_strike['charm'] < 0].sort_values('charm').head(3)
    
    # Create a grid-like bar chart
    fig = go.Figure()
    
    # Add bars with borders
    for i, row in charm_by_strike.iterrows():
        strike = row['strike']
        charm_value = row['charm']
        
        # Check if this strike is in the top positive or negative values
        is_top_positive = strike in top_positive['strike'].values
        is_top_negative = strike in top_negative['strike'].values
        
        # Determine color based on value
        if is_top_positive:
            color = '#00bc8c'  # Bright green for top positive
            border_width = 3
        elif is_top_negative:
            color = '#e74c3c'  # Bright red for top negative
            border_width = 3
        else:
            # Regular colors for non-top values
            color = 'rgba(0, 188, 140, 0.5)' if charm_value >= 0 else 'rgba(231, 76, 60, 0.5)'
            border_width = 1
        
        # Add a bar with border - no legend entries
        fig.add_trace(go.Bar(
            x=[strike],
            y=[charm_value],
            showlegend=False,
            marker=dict(
                color=color,
                line=dict(
                    color='rgba(255, 255, 255, 0.8)',
                    width=border_width
                )
            ),
            width=10,  # Adjust width to create grid-like appearance
            text=f"{int(charm_value):,}" if (is_top_positive or is_top_negative) else None,
            textposition='auto',
            hovertemplate='Strike: %{x}<br>Charm: %{y:,.0f}<extra></extra>'
        ))
    
    # Add vertical line for current price
    fig.add_vline(
        x=current_price, 
        line_width=2, 
        line_dash="dash", 
        line_color="#3498db",
        annotation_text="Current Price", 
        annotation_position="top right",
        annotation=dict(font=dict(color="#3498db", size=12))
    )
    
    # Calculate net charm
    net_charm = selected_data['charm'].sum()
    
    # Find charm flip level (where charm exposure changes sign)
    charm_by_strike = charm_by_strike.sort_values('strike')
    charm_signs = np.sign(charm_by_strike['charm'])
    sign_changes = ((charm_signs[:-1] * charm_signs[1:]) < 0).values
    
    if any(sign_changes):
        flip_indices = np.where(sign_changes)[0]
        flip_levels = []
        for idx in flip_indices:
            flip_levels.append((charm_by_strike['strike'].iloc[idx] + charm_by_strike['strike'].iloc[idx+1])/2)
        
        # Add charm flip level lines with distinctive styling - using teal dotted lines
        for level in flip_levels:
            fig.add_vline(
                x=level, 
                line_width=2, 
                line_dash="dot", 
                line_color="#1abc9c",  # Teal color (different from gamma's purple)
                annotation_text="Charm Flip", 
                annotation_position="top right",
                annotation=dict(font=dict(color="#1abc9c", size=12))
            )
    
    fig.update_layout(
        title=dict(
            text=f"Charm Exposure (Delta Decay) by Strike (DTE: {selected_dte if selected_dte >= 0 else 'All'})",
            font=dict(size=18, color="#f8f9fa")
        ),
        xaxis_title=dict(text="Strike Price", font=dict(size=14, color="#f8f9fa")),
        yaxis_title=dict(text="Charm (Delta Decay per Day)", font=dict(size=14, color="#f8f9fa")),
        height=650,  # Further increased height
        template="plotly_dark",
        plot_bgcolor='#1e1e1e',
        paper_bgcolor='#1e1e1e',
        font=dict(color='#f8f9fa'),
        margin=dict(l=40, r=40, t=60, b=250),  # Dramatically increased bottom margin
        barmode='relative',
        bargap=0.05,  # Small gap between bars
    )
    
    # Add zero line
    fig.add_hline(y=0, line_width=1, line_dash="dash", line_color="rgba(255,255,255,0.3)")
    
    # Create negative charm annotation - separate boxes for each value
    if not top_negative.empty:
        # Add title for negative charm
        fig.add_annotation(
            x=0.5,
            y=-0.25,  # Position for title
            xref="paper",
            yref="paper",
            text="<span style='color:#e74c3c'>Negative Charm:</span>",
            showarrow=False,
            font=dict(size=12, color="#f8f9fa"),
            align="center",
            bgcolor="rgba(30, 30, 30, 0.7)",
            bordercolor="rgba(255, 255, 255, 0.3)",
            borderwidth=1,
            borderpad=4,
            opacity=0.9
        )
        
        # Fixed positions for a symmetrical layout - 3 boxes
        positions = [0.25, 0.5, 0.75]  # Left, center, right
        
        # Add individual boxes for each negative charm - centered positions
        for i, row in enumerate(top_negative.iterrows()):
            if i >= len(positions):  # Skip if we have more than 3 items
                break
                
            idx, row_data = row
            charm_value = int(row_data['charm'])
            strike = row_data['strike']
            
            fig.add_annotation(
                x=positions[i],  # Use fixed position
                y=-0.32,  # Position below the title
                xref="paper",
                yref="paper",
                text=f"<span style='color:#e74c3c'>{charm_value:,}@{strike:.0f}</span>",
                showarrow=False,
                font=dict(size=10, color="#f8f9fa"),
                align="center",
                bgcolor="rgba(30, 30, 30, 0.7)",
                bordercolor="rgba(231, 76, 60, 0.5)",
                borderwidth=2,
                borderpad=4,
                opacity=0.9
            )
    
    # Create positive charm annotation - separate boxes for each value
    if not top_positive.empty:
        # Add title for positive charm
        fig.add_annotation(
            x=0.5,
            y=-0.42,  # Position below negative charm
            xref="paper",
            yref="paper",
            text="<span style='color:#00bc8c'>Positive Charm:</span>",
            showarrow=False,
            font=dict(size=12, color="#f8f9fa"),
            align="center",
            bgcolor="rgba(30, 30, 30, 0.7)",
            bordercolor="rgba(255, 255, 255, 0.3)",
            borderwidth=1,
            borderpad=4,
            opacity=0.9
        )
        
        # Add individual boxes for each positive charm - centered positions
        for i, row in enumerate(top_positive.iterrows()):
            if i >= len(positions):  # Skip if we have more than 3 items
                break
                
            idx, row_data = row
            charm_value = int(row_data['charm'])
            strike = row_data['strike']
            
            fig.add_annotation(
                x=positions[i],  # Use fixed position
                y=-0.49,  # Position below the title
                xref="paper",
                yref="paper",
                text=f"<span style='color:#00bc8c'>{charm_value:,}@{strike:.0f}</span>",
                showarrow=False,
                font=dict(size=10, color="#f8f9fa"),
                align="center",
                bgcolor="rgba(30, 30, 30, 0.7)",
                bordercolor="rgba(0, 188, 140, 0.5)",
                borderwidth=2,
                borderpad=4,
                opacity=0.9
            )
    
    # Add net charm annotation
    fig.add_annotation(
        x=0.5,
        y=-0.59,  # Position below positive charm
        xref="paper",
        yref="paper",
        text=f"<span style='color:#f8f9fa'>Net Charm: {net_charm:,.0f}</span>",
        showarrow=False,
        font=dict(size=12, color="#f8f9fa"),
        align="center",
        bgcolor="rgba(30, 30, 30, 0.7)",
        bordercolor="rgba(255, 255, 255, 0.3)",
        borderwidth=1,
        borderpad=4,
        opacity=0.9
    )
    
    return fig

def calculate_greeks(options, current_price):
    """Calculate additional Greeks for options data"""
    # Make a copy to avoid modifying the original
    options = options.copy()
    
    # Calculate dealer delta position (inverse of market position)
    options['dealer_delta_position'] = options.apply(
        lambda row: row['delta_exposure'] if row['type'] == 'call' else -row['delta_exposure'], 
        axis=1
    )
    
    # Calculate charm (delta decay) if not already present
    if 'charm' not in options.columns:
        # Approximate charm calculation
        # Charm is the rate of change of delta with respect to time
        # We'll use a simplified model based on Black-Scholes approximations
        
        options['charm'] = options.apply(
            lambda row: calculate_charm(
                current_price, 
                row['strike'], 
                row['dte'] / 365, 
                0.02,  # risk-free rate (approximate)
                row['impliedVolatility'] if not pd.isna(row['impliedVolatility']) else 0.3,
                row['type'] == 'call'
            ) * row['openInterest'] * 100,  # Scale by contract size
            axis=1
        )
    
    return options

def calculate_charm(S, K, T, r, sigma, is_call):
    """Calculate charm (delta decay) using Black-Scholes approximation"""
    if T <= 0.001:  # Avoid division by zero or negative time
        return 0
        
    # Calculate d1 and d2
    d1 = (np.log(S/K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
    d2 = d1 - sigma * np.sqrt(T)
    
    # Standard normal PDF and CDF
    norm_pdf = lambda x: np.exp(-0.5 * x**2) / np.sqrt(2 * np.pi)
    norm_cdf = lambda x: 0.5 * (1 + np.math.erf(x / np.sqrt(2)))
    
    # Calculate charm
    if is_call:
        charm = -norm_pdf(d1) * (r / (sigma * np.sqrt(T)) - d2 / (2 * T))
    else:
        charm = -norm_pdf(d1) * (r / (sigma * np.sqrt(T)) - d2 / (2 * T))
        charm = -charm  # Flip sign for puts
        
    return charm

# Add this function to calculate put/call ratio over time
def plot_put_call_ratio(options, price_data, current_price):
    """Plot the put/call ratio trend over time"""
    fig = go.Figure()
    
    # Group options by expiry and calculate P/C ratios
    if not options.empty:
        # Check if we have the necessary columns
        if 'expiry' not in options.columns or 'type' not in options.columns:
            # Create a placeholder figure with a message
            fig.add_annotation(
                x=0.5, y=0.5, xref="paper", yref="paper",
                text="Insufficient data to calculate Put/Call ratio",
                showarrow=False,
                font=dict(size=14, color="white")
            )
            return fig
        
        # Group by expiry and calculate volume and OI for puts and calls
        daily_stats = options.groupby(['expiry', 'type']).agg({
            'volume': 'sum',
            'openInterest': 'sum'
        }).reset_index()
        
        # Pivot to get puts and calls in separate columns
        volume_pivot = daily_stats.pivot(index='expiry', columns='type', values='volume').reset_index()
        oi_pivot = daily_stats.pivot(index='expiry', columns='type', values='openInterest').reset_index()
        
        # Ensure we have both call and put columns
        if 'call' not in volume_pivot.columns or 'put' not in volume_pivot.columns:
            # Create a placeholder figure with a message
            fig.add_annotation(
                x=0.5, y=0.5, xref="paper", yref="paper",
                text="Insufficient data to calculate Put/Call ratio",
                showarrow=False,
                font=dict(size=14, color="white")
            )
            return fig
            
        # Rename columns for clarity
        volume_pivot.columns = ['date', 'call_volume', 'put_volume']
        oi_pivot.columns = ['date', 'call_oi', 'put_oi']
        
        # Merge the dataframes
        ratio_df = pd.merge(volume_pivot, oi_pivot, on='date')
        
        # Calculate ratios (with safety checks)
        ratio_df['pc_vol_ratio'] = ratio_df['put_volume'] / ratio_df['call_volume'].replace(0, 1)
        ratio_df['pc_oi_ratio'] = ratio_df['put_oi'] / ratio_df['call_oi'].replace(0, 1)
        
        # Cap extreme values for better visualization
        max_ratio = 5.0
        ratio_df['pc_vol_ratio'] = ratio_df['pc_vol_ratio'].clip(upper=max_ratio)
        ratio_df['pc_oi_ratio'] = ratio_df['pc_oi_ratio'].clip(upper=max_ratio)
        
        # Sort by date
        ratio_df = ratio_df.sort_values('date')
        
        # Format dates for cleaner display
        # Convert to datetime if not already and format to simple date strings
        ratio_df['formatted_date'] = pd.to_datetime(ratio_df['date']).dt.strftime('%b %d')
        
        # Add volume-based P/C ratio as bars
        fig.add_trace(go.Bar(
            x=ratio_df['formatted_date'],
            y=ratio_df['pc_vol_ratio'],
            name='Volume P/C Ratio',
            marker_color='#00bc8c',
            text=[f'{ratio:.2f}' for ratio in ratio_df['pc_vol_ratio']],
            textposition='auto',
            opacity=0.7,
            width=0.4,  # Make bars narrower
            offset=-0.2  # Shift to the left
        ))
        
        # Add OI-based P/C ratio as bars
        fig.add_trace(go.Bar(
            x=ratio_df['formatted_date'],
            y=ratio_df['pc_oi_ratio'],
            name='OI P/C Ratio',
            marker_color='#3498db',
            text=[f'{ratio:.2f}' for ratio in ratio_df['pc_oi_ratio']],
            textposition='auto',
            opacity=0.7,
            width=0.4,  # Make bars narrower
            offset=0.2  # Shift to the right
        ))
        
        # Add reference line at 1.0
        fig.add_hline(y=1.0, line_width=1, line_dash="dash", line_color="white",
                     annotation_text="Bullish/Bearish Threshold", annotation_position="right")
        
        # Add annotations for sentiment zones
        fig.add_annotation(
            x=0.02, y=1.5, xref="paper", yref="y",
            text="Bearish Zone",
            showarrow=False,
            font=dict(color="#e74c3c", size=14)
        )
        
        fig.add_annotation(
            x=0.02, y=0.5, xref="paper", yref="y",
            text="Bullish Zone",
            showarrow=False,
            font=dict(color="#00bc8c", size=14)
        )
        
        # Add note about capped values
        fig.add_annotation(
            x=0.5, y=0.95, xref="paper", yref="paper",
            text=f"Note: Some extreme values are capped at {max_ratio} for better visualization",
            showarrow=False,
            font=dict(size=10, color="white"),
            bgcolor="rgba(0,0,0,0.7)",
            bordercolor="white",
            borderwidth=1,
            borderpad=3,
            opacity=0.8
        )
    else:
        # Create a placeholder figure with a message
        fig.add_annotation(
            x=0.5, y=0.5, xref="paper", yref="paper",
            text="No options data available",
            showarrow=False,
            font=dict(size=14, color="white")
        )
    
    # Update layout with cleaner x-axis
    fig.update_layout(
        title="Put/Call Ratio by Expiration",
        xaxis_title="Expiration Date",
        yaxis_title="Put/Call Ratio (capped at 5)",
        template="plotly_dark",
        plot_bgcolor='#1e1e1e',
        paper_bgcolor='#1e1e1e',
        font=dict(color='#f8f9fa'),
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        yaxis=dict(
            range=[0, 5]  # Set y-axis range from 0 to 5
        ),
        xaxis=dict(
            tickangle=0,  # Horizontal labels
            tickfont=dict(size=12),  # Larger font
            showgrid=True,
            gridcolor="rgba(255,255,255,0.1)"
        )
    )
    
    return fig

# Add this function to detect unusual options activity
def plot_unusual_activity(options, current_price):
    if options.empty:
        return go.Figure()
    
    # Calculate volume to open interest ratio
    options['vol_to_oi'] = options['volume'] / options['openInterest'].replace(0, 1)
    
    # Filter for unusual activity (vol/oi > 1.0)
    unusual = options[options['vol_to_oi'] > 1.0].copy()
    
    if unusual.empty:
        fig = go.Figure()
        fig.update_layout(
            title="No Unusual Options Activity Detected",
            template="plotly_dark"
        )
        return fig
    
    # Find top 10 most unusual
    top_unusual = unusual.nlargest(10, 'vol_to_oi')
    
    # Create figure
    fig = go.Figure()
    
    # Add bars for calls
    call_data = top_unusual[top_unusual['type'] == 'call']
    if not call_data.empty:
        fig.add_trace(go.Bar(
            x=call_data['strike'],
            y=call_data['vol_to_oi'],
            name='Calls',
            marker_color='#00bc8c',
            text=call_data['volume'].astype(int),
            textposition='auto',
            hovertemplate='Strike: %{x}<br>Vol/OI: %{y:.2f}<br>Volume: %{text:,}<extra></extra>'
        ))
    
    # Add bars for puts
    put_data = top_unusual[top_unusual['type'] == 'put']
    if not put_data.empty:
        fig.add_trace(go.Bar(
            x=put_data['strike'],
            y=put_data['vol_to_oi'],
            name='Puts',
            marker_color='#e74c3c',
            text=put_data['volume'].astype(int),
            textposition='auto',
            hovertemplate='Strike: %{x}<br>Vol/OI: %{y:.2f}<br>Volume: %{text:,}<extra></extra>'
        ))
    
    # Add vertical line for current price
    fig.add_vline(
        x=current_price, 
        line_width=2, 
        line_dash="dash", 
        line_color="#3498db",
        annotation_text="Current Price", 
        annotation_position="top right"
    )
    
    # Update layout
    fig.update_layout(
        title="Unusual Options Activity (Volume/OI > 1.0)",
        xaxis_title="Strike Price",
        yaxis_title="Volume/Open Interest Ratio",
        template="plotly_dark",
        legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
        margin=dict(l=40, r=40, t=40, b=40)
    )
    
    return fig

# Add this function to create a fear/greed index
def plot_fear_greed_index(options, current_price):
    if options.empty:
        return go.Figure()
    
    # Calculate components for fear/greed index
    
    # 1. Put/Call Ratio (higher = more fear)
    calls = options[options['type'] == 'call']
    puts = options[options['type'] == 'put']
    
    call_volume = calls['volume'].sum()
    put_volume = puts['volume'].sum()
    pc_ratio = put_volume / max(1, call_volume)
    
    # Normalize to 0-100 scale (1.5+ is high fear, 0.5 or less is high greed)
    pc_score = max(0, min(100, 100 - ((pc_ratio - 0.5) / 1.0) * 100))
    
    # 2. IV Skew (higher put IV vs call IV = more fear)
    atm_calls = calls[(calls['strike'] >= current_price * 0.98) & (calls['strike'] <= current_price * 1.02)]
    atm_puts = puts[(puts['strike'] >= current_price * 0.98) & (puts['strike'] <= current_price * 1.02)]
    
    if not atm_calls.empty and not atm_puts.empty:
        avg_call_iv = atm_calls['impliedVolatility'].mean()
        avg_put_iv = atm_puts['impliedVolatility'].mean()
        iv_skew = avg_put_iv - avg_call_iv
        
        # Normalize to 0-100 (0.05+ skew is high fear, -0.02 or less is high greed)
        iv_score = max(0, min(100, 100 - ((iv_skew + 0.02) / 0.07) * 100))
    else:
        iv_score = 50  # Neutral if no data
    
    # 3. OTM Put Activity (higher = more fear)
    otm_puts = puts[puts['strike'] < current_price * 0.95]
    atm_itm_puts = puts[puts['strike'] >= current_price * 0.95]
    
    otm_put_volume = otm_puts['volume'].sum()
    atm_itm_put_volume = atm_itm_puts['volume'].sum()
    
    if otm_put_volume + atm_itm_put_volume > 0:
        otm_put_ratio = otm_put_volume / (otm_put_volume + atm_itm_put_volume)
        
        # Normalize to 0-100 (0.7+ is high fear, 0.3 or less is high greed)
        otm_score = max(0, min(100, 100 - ((otm_put_ratio - 0.3) / 0.4) * 100))
    else:
        otm_score = 50  # Neutral if no data
    
    # Calculate composite score (weighted average)
    composite_score = (pc_score * 0.4) + (iv_score * 0.4) + (otm_score * 0.2)
    
    # Create gauge chart
    fig = go.Figure(go.Indicator(
        mode="gauge+number",
        value=composite_score,
        title={'text': "Options-Based Fear/Greed Index"},
        gauge={
            'axis': {'range': [0, 100], 'tickwidth': 1, 'tickcolor': "white"},
            'bar': {'color': "rgba(255,255,255,0.1)"},
            'steps': [
                {'range': [0, 25], 'color': "#00bc8c"},  # Extreme Greed
                {'range': [25, 45], 'color': "#2ecc71"},  # Greed
                {'range': [45, 55], 'color': "#f39c12"},  # Neutral
                {'range': [55, 75], 'color': "#e74c3c"},  # Fear
                {'range': [75, 100], 'color': "#c0392b"}  # Extreme Fear
            ],
            'threshold': {
                'line': {'color': "white", 'width': 2},
                'thickness': 0.75,
                'value': composite_score
            }
        }
    ))
    
    # Add annotations for components
    fig.add_annotation(
        x=0.5, y=0.25, xref="paper", yref="paper",
        text=f"P/C Ratio: {pc_ratio:.2f} (Score: {pc_score:.0f})",
        showarrow=False,
        font=dict(color="white", size=12)
    )
    
    fig.add_annotation(
        x=0.5, y=0.2, xref="paper", yref="paper",
        text=f"IV Skew: {iv_skew:.2f} (Score: {iv_score:.0f})" if 'iv_skew' in locals() else "IV Skew: N/A",
        showarrow=False,
        font=dict(color="white", size=12)
    )
    
    fig.add_annotation(
        x=0.5, y=0.15, xref="paper", yref="paper",
        text=f"OTM Put Activity: {otm_put_ratio:.2f} (Score: {otm_score:.0f})" if 'otm_put_ratio' in locals() else "OTM Put Activity: N/A",
        showarrow=False,
        font=dict(color="white", size=12)
    )
    
    # Update layout
    fig.update_layout(
        template="plotly_dark",
        margin=dict(l=40, r=40, t=60, b=40)
    )
    
    return fig

# Create a header with logo and title
header = dbc.Navbar(
    dbc.Container(
        [
            html.A(
                dbc.Row(
                    [
                        dbc.Col(html.I(className="fas fa-chart-line", style={"fontSize": "2rem"})),
                        dbc.Col(dbc.NavbarBrand("SPX Dealer Positioning Dashboard", className="ms-2")),
                    ],
                    align="center",
                ),
                href="#",
                style={"textDecoration": "none"},
            ),
            dbc.NavbarToggler(id="navbar-toggler"),
            dbc.Collapse(
                dbc.Nav(
                    [
                        dbc.NavItem(dbc.NavLink("Dashboard", href="#")),
                        dbc.NavItem(dbc.NavLink("About", href="#")),
                    ],
                    className="ms-auto",
                    navbar=True,
                ),
                id="navbar-collapse",
                navbar=True,
            ),
        ]
    ),
    color="dark",
    dark=True,
    className="mb-4",
)

# Improved layout with cards and better organization
app.layout = html.Div([
    # Header
    header,
    
    # Main container
    dbc.Container([
        # Info and controls row
        dbc.Row([
            # Info card
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader("Dashboard Information"),
                    dbc.CardBody([
                        html.P("This dashboard shows dealer hedging positions based on options greeks:"),
                        html.Ul([
                            html.Li("Positive dealer delta means dealers are net long (need to sell futures to hedge)"),
                            html.Li("Negative dealer delta means dealers are net short (need to buy futures to hedge)"),
                            html.Li("Gamma exposure shows where dealers need to adjust hedges as price moves"),
                            html.Li("Purple dotted lines show gamma flip levels where dealer hedging behavior changes"),
                        ]),
                    ])
                ])
            ], md=8),
            
            # Controls card
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader("Controls"),
                    dbc.CardBody([
                        html.Label("Select Expiration:"),
                        dcc.Dropdown(
                            id='dte-selector',
                            options=[
                                {'label': '0 DTE (Today)', 'value': 0},
                                {'label': '1 DTE', 'value': 1},
                                {'label': '2 DTE', 'value': 2},
                                {'label': '7 DTE', 'value': 7},
                                {'label': 'All Available', 'value': -1}
                            ],
                            value=0,  # Default to 0DTE
                            clearable=False,
                            className="mb-3"
                        ),
                        dbc.Button('Refresh Data', id='refresh-button', color="success", className="w-100"),
                        html.Div(id='last-update-time', className="mt-2 text-center text-muted small")
                    ])
                ])
            ], md=4),
        ], className="mb-4"),
        
        # Tabs for different sections
        dbc.Tabs([
            # Tab 1: Dealer Positioning (Original content)
            dbc.Tab([
                # Main chart
                dbc.Card([
                    dbc.CardHeader("SPX Dealer Positioning"),
                    dbc.CardBody([
                        dcc.Graph(id='live-graph')
                    ])
                ], className="mb-4"),
                
                # Secondary charts row
                dbc.Row([
                    # Hedging flows
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader("Hedging Flows"),
                            dbc.CardBody([
                                dcc.Graph(id='hedging-flows')
                            ])
                        ])
                    ], md=6),
                    
                    # Charm exposure
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader("Charm Exposure"),
                            dbc.CardBody([
                                dcc.Graph(id='charm-exposure')
                            ])
                        ])
                    ], md=6),
                ], className="mb-4"),
                
                # Gamma heatmap
                dbc.Card([
                    dbc.CardHeader("Gamma Exposure Heatmap"),
                    dbc.CardBody([
                        dcc.Graph(id='gamma-heatmap')
                    ])
                ], className="mb-4"),
            ], label="Dealer Positioning", tab_id="tab-dealer-positioning"),
            
            # Tab 2: Options Sentiment (New tab)
            dbc.Tab([
                dbc.Row([
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader("Put/Call Ratio Tracker"),
                            dbc.CardBody([
                                dcc.Graph(id='put-call-ratio-graph')
                            ])
                        ])
                    ], md=6),
                    
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader("Options-Based Fear/Greed Index"),
                            dbc.CardBody([
                                dcc.Graph(id='fear-greed-index')
                            ])
                        ])
                    ], md=6)
                ], className="mb-4"),
                
                dbc.Card([
                    dbc.CardHeader("Unusual Options Activity"),
                    dbc.CardBody([
                        dcc.Graph(id='unusual-options-activity')
                    ])
                ], className="mb-4")
            ], label="Options Sentiment", tab_id="tab-options-sentiment"),
        ], id="tabs", active_tab="tab-dealer-positioning"),
        
        dcc.Interval(id='interval-component', interval=300*1000, n_intervals=0)  # 5 minutes
    ])
], style=custom_css)

# Update the callback to include the new graphs
@app.callback(
    [Output('live-graph', 'figure'),
     Output('hedging-flows', 'figure'),
     Output('gamma-heatmap', 'figure'),
     Output('charm-exposure', 'figure'),
     Output('put-call-ratio-graph', 'figure'),
     Output('unusual-options-activity', 'figure'),
     Output('fear-greed-index', 'figure'),
     Output('last-update-time', 'children')],
    [Input('dte-selector', 'value'),
     Input('refresh-button', 'n_clicks'),
     Input('interval-component', 'n_intervals'),
     Input('tabs', 'active_tab')]
)
def update_dashboard(selected_dte, n_clicks, n_intervals, active_tab):
    # Get the current time for display
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Determine if this is a forced refresh
    ctx = callback_context
    if ctx.triggered:
        trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]
        force_refresh = trigger_id == 'refresh-button'
    else:
        force_refresh = False
    
    # Fetch data - pass the selected_dte to optimize fetching
    options_data, price_data, expiry, current_price, all_options = fetch_options_data(
        force_refresh=force_refresh,
        selected_dte=selected_dte
    )
    
    # Filter data based on selected DTE if applicable
    filtered_options = options_data
    if selected_dte >= 0 and not options_data.empty:
        # Get list of available expiries
        available_expiries = sorted(options_data['expiry'].unique())
        
        # Select the expiry that corresponds to the selected DTE
        if selected_dte < len(available_expiries):
            selected_expiry = available_expiries[selected_dte]
            filtered_options = options_data[options_data['expiry'] == selected_expiry]
            expiry = selected_expiry
    
    # Generate all charts with the selected DTE
    main_chart = plot_dashboard(filtered_options, price_data, expiry, current_price, all_options, selected_dte)
    hedging_flows = plot_hedging_flows(filtered_options, current_price, selected_dte)
    gamma_heatmap = plot_gamma_heatmap(all_options, current_price, selected_dte)
    charm_exposure = plot_charm_exposure(filtered_options, current_price, selected_dte)
    
    # Generate new sentiment charts
    put_call_ratio = plot_put_call_ratio(all_options, price_data, current_price)
    unusual_activity = plot_unusual_activity(filtered_options, current_price)
    fear_greed = plot_fear_greed_index(filtered_options, current_price)
    
    # Update the last update time display
    last_update_text = f"Last updated: {current_time}"
    
    return main_chart, hedging_flows, gamma_heatmap, charm_exposure, put_call_ratio, unusual_activity, fear_greed, last_update_text

if __name__ == '__main__':
    app.run(debug=True, port=8050)
