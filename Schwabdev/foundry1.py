import streamlit as st
import yfinance as yf
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
from datetime import datetime, timedelta
import time
import warnings
from scipy.stats import norm
warnings.filterwarnings('ignore')

# Page configuration
st.set_page_config(
    page_title="Options Flow Dashboard",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Custom CSS for dark theme matching the original
st.markdown("""
<style>
    .stApp {
        background-color: #0E1117;
        color: #FAFAFA;
    }
    
    .main-header {
        background: linear-gradient(90deg, #1f2937 0%, #374151 100%);
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
        border: 1px solid #374151;
    }
    
    .metric-container {
        background: #1f2937;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #374151;
    }
    
    .chart-container {
        background: #1f2937;
        border-radius: 0.5rem;
        border: 1px solid #374151;
        padding: 0.5rem;
    }
    
    div[data-testid="stMetricValue"] {
        color: #60A5FA;
    }
    
    .stSelectbox > div > div {
        background-color: #374151;
        color: #FAFAFA;
    }
    
    .stButton > button {
        background-color: #1f2937;
        color: #FAFAFA;
        border: 1px solid #374151;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'last_update' not in st.session_state:
    st.session_state.last_update = datetime.now()
if 'auto_refresh' not in st.session_state:
    st.session_state.auto_refresh = True
if 'chart_zoom_ranges' not in st.session_state:
    st.session_state.chart_zoom_ranges = {}
if 'selected_expirations' not in st.session_state:
    st.session_state.selected_expirations = {}

class OptionsFlowDashboard:
    def __init__(self):
        self.symbols = ['SPY', 'QQQ', '^SPX', 'ES=F', 'NQ=F']
        self.symbol_names = {
            'SPY': 'SPDR S&P 500 ETF',
            'QQQ': 'Invesco QQQ ETF',
            '^SPX': 'S&P 500 Index',
            'ES=F': 'E-mini S&P 500',
            'NQ=F': 'E-mini NASDAQ-100'
        }
        
    def fetch_options_data(self, symbol, expiration_date=None):
        """Fetch options data for a given symbol and expiration"""
        try:
            ticker = yf.Ticker(symbol)

            # Get current price
            hist = ticker.history(period="1d")
            current_price = hist['Close'].iloc[-1] if not hist.empty else None

            # Get options dates
            options_dates = ticker.options
            if not options_dates:
                return None, None, current_price, []

            # Use specified expiration or nearest
            target_exp = expiration_date if expiration_date else options_dates[0]

            # Fetch options chain
            options_chain = ticker.option_chain(target_exp)
            calls = options_chain.calls
            puts = options_chain.puts

            return calls, puts, current_price, list(options_dates)

        except Exception as e:
            st.error(f"Error fetching data for {symbol}: {str(e)}")
            return None, None, None, []

    def fetch_multiple_expirations_data(self, symbol, expiration_dates):
        """Fetch options data for multiple expirations"""
        all_calls = []
        all_puts = []
        current_price = None

        try:
            ticker = yf.Ticker(symbol)

            # Get current price
            hist = ticker.history(period="1d")
            current_price = hist['Close'].iloc[-1] if not hist.empty else None

            for exp_date in expiration_dates:
                try:
                    options_chain = ticker.option_chain(exp_date)
                    calls = options_chain.calls.copy()
                    puts = options_chain.puts.copy()

                    # Add expiration date column
                    calls['expiration'] = exp_date
                    puts['expiration'] = exp_date

                    # Calculate days to expiration
                    exp_datetime = datetime.strptime(exp_date, '%Y-%m-%d')
                    days_to_exp = (exp_datetime - datetime.now()).days
                    calls['daysToExpiration'] = days_to_exp
                    puts['daysToExpiration'] = days_to_exp

                    all_calls.append(calls)
                    all_puts.append(puts)

                except Exception as e:
                    st.warning(f"Could not fetch data for {exp_date}: {str(e)}")
                    continue

            # Combine all data
            combined_calls = pd.concat(all_calls, ignore_index=True) if all_calls else pd.DataFrame()
            combined_puts = pd.concat(all_puts, ignore_index=True) if all_puts else pd.DataFrame()

            return combined_calls, combined_puts, current_price

        except Exception as e:
            st.error(f"Error fetching multi-expiration data for {symbol}: {str(e)}")
            return pd.DataFrame(), pd.DataFrame(), None
    
    def calculate_oi_weighted_exposure(self, calls, puts, current_price):
        """Calculate Open Interest weighted exposure"""
        if calls is None or puts is None:
            return pd.DataFrame(), pd.DataFrame()
        
        # Filter for strikes within reasonable range (tighter focus for better zoom)
        price_range = current_price * 0.15  # 15% range (tighter than before)
        min_strike = current_price - price_range
        max_strike = current_price + price_range
        
        calls_filtered = calls[(calls['strike'] >= min_strike) & (calls['strike'] <= max_strike)]
        puts_filtered = puts[(puts['strike'] >= min_strike) & (puts['strike'] <= max_strike)]
        
        # Calculate exposure (simplified gamma exposure approximation)
        calls_filtered = calls_filtered.copy()
        puts_filtered = puts_filtered.copy()
        
        # Approximate gamma exposure
        calls_filtered['exposure'] = calls_filtered['openInterest'] * calls_filtered['volume'].fillna(0) * 100
        puts_filtered['exposure'] = puts_filtered['openInterest'] * puts_filtered['volume'].fillna(0) * 100 * -1
        
        return calls_filtered, puts_filtered
    
    def create_oi_exposure_chart(self, calls, puts, current_price, symbol, chart_id="oi_exposure"):
        """Create OI Weighted Exposure chart with persistent zoom"""
        fig = go.Figure()

        if calls is not None and not calls.empty:
            # Group by expiration if multiple expirations exist
            if 'expiration' in calls.columns:
                for exp_date in calls['expiration'].unique():
                    exp_calls = calls[calls['expiration'] == exp_date]
                    fig.add_trace(go.Bar(
                        x=exp_calls['exposure'],
                        y=exp_calls['strike'],
                        orientation='h',
                        name=f'Call Impact ({exp_date})',
                        marker_color='#60A5FA',
                        opacity=0.7
                    ))
            else:
                fig.add_trace(go.Bar(
                    x=calls['exposure'],
                    y=calls['strike'],
                    orientation='h',
                    name='Call Impact',
                    marker_color='#60A5FA',
                    opacity=0.8
                ))

        if puts is not None and not puts.empty:
            # Group by expiration if multiple expirations exist
            if 'expiration' in puts.columns:
                for exp_date in puts['expiration'].unique():
                    exp_puts = puts[puts['expiration'] == exp_date]
                    fig.add_trace(go.Bar(
                        x=exp_puts['exposure'],
                        y=exp_puts['strike'],
                        orientation='h',
                        name=f'Put Impact ({exp_date})',
                        marker_color='#EF4444',
                        opacity=0.7
                    ))
            else:
                fig.add_trace(go.Bar(
                    x=puts['exposure'],
                    y=puts['strike'],
                    orientation='h',
                    name='Put Impact',
                    marker_color='#EF4444',
                    opacity=0.8
                ))

        # Add current price line
        if current_price:
            fig.add_hline(y=current_price, line_dash="dash", line_color="white",
                         line_width=2, annotation_text=f"Current: ${current_price:.2f}",
                         annotation_position="top right")

        # Apply persistent zoom if available, otherwise set default zoom around current price
        zoom_key = f"{symbol}_{chart_id}"
        if zoom_key in st.session_state.chart_zoom_ranges:
            zoom_range = st.session_state.chart_zoom_ranges[zoom_key]
            fig.update_layout(
                xaxis=dict(range=zoom_range.get('x', [None, None])),
                yaxis=dict(range=zoom_range.get('y', [None, None]))
            )
        elif current_price:
            # Default zoom: ±8% around current price for Y-axis (tighter focus)
            price_range = current_price * 0.08
            default_y_range = [current_price - price_range, current_price + price_range]
            fig.update_layout(yaxis=dict(range=default_y_range))

        fig.update_layout(
            title=f"{symbol} - OI Weighted Exposure",
            xaxis_title="Exposure",
            yaxis_title="Strike Price",
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white'),
            showlegend=True,
            height=500,
            uirevision=zoom_key  # This helps maintain zoom state
        )

        fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='rgba(255,255,255,0.1)')
        fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='rgba(255,255,255,0.1)')

        return fig
    
    def create_volume_breakdown_chart(self, calls, puts, current_price, symbol, chart_id="volume_breakdown"):
        """Create Volume Bought and Sold chart with current price and persistent zoom"""
        fig = go.Figure()

        if calls is not None and not calls.empty:
            # Group by expiration if multiple expirations exist
            if 'expiration' in calls.columns:
                for exp_date in calls['expiration'].unique():
                    exp_calls = calls[calls['expiration'] == exp_date]
                    fig.add_trace(go.Bar(
                        x=exp_calls['volume'].fillna(0),
                        y=exp_calls['strike'],
                        orientation='h',
                        name=f'Call Volume ({exp_date})',
                        marker_color='#60A5FA',
                        opacity=0.7
                    ))
            else:
                fig.add_trace(go.Bar(
                    x=calls['volume'].fillna(0),
                    y=calls['strike'],
                    orientation='h',
                    name='Call Volume',
                    marker_color='#60A5FA',
                    opacity=0.8
                ))

        if puts is not None and not puts.empty:
            # Group by expiration if multiple expirations exist
            if 'expiration' in puts.columns:
                for exp_date in puts['expiration'].unique():
                    exp_puts = puts[puts['expiration'] == exp_date]
                    fig.add_trace(go.Bar(
                        x=exp_puts['volume'].fillna(0) * -1,
                        y=exp_puts['strike'],
                        orientation='h',
                        name=f'Put Volume ({exp_date})',
                        marker_color='#EF4444',
                        opacity=0.7
                    ))
            else:
                fig.add_trace(go.Bar(
                    x=puts['volume'].fillna(0) * -1,
                    y=puts['strike'],
                    orientation='h',
                    name='Put Volume',
                    marker_color='#EF4444',
                    opacity=0.8
                ))

        # Add current price line
        if current_price:
            fig.add_hline(y=current_price, line_dash="dash", line_color="white",
                         line_width=2, annotation_text=f"Current: ${current_price:.2f}",
                         annotation_position="top right")

        # Apply persistent zoom if available, otherwise set default zoom around current price
        zoom_key = f"{symbol}_{chart_id}"
        if zoom_key in st.session_state.chart_zoom_ranges:
            zoom_range = st.session_state.chart_zoom_ranges[zoom_key]
            fig.update_layout(
                xaxis=dict(range=zoom_range.get('x', [None, None])),
                yaxis=dict(range=zoom_range.get('y', [None, None]))
            )
        elif current_price:
            # Default zoom: ±8% around current price for Y-axis (tighter focus)
            price_range = current_price * 0.08
            default_y_range = [current_price - price_range, current_price + price_range]
            fig.update_layout(yaxis=dict(range=default_y_range))

        fig.update_layout(
            title=f"{symbol} - Volume Breakdown",
            xaxis_title="Volume",
            yaxis_title="Strike Price",
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white'),
            showlegend=True,
            height=500,
            uirevision=zoom_key
        )

        fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='rgba(255,255,255,0.1)')
        fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='rgba(255,255,255,0.1)')

        return fig
    
    def create_net_flow_chart(self, calls, puts, current_price, symbol, chart_id="net_flow"):
        """Create Net Flow by Strike chart with current price and persistent zoom"""
        fig = go.Figure()

        if calls is not None and puts is not None and not calls.empty and not puts.empty:
            # Calculate net flow (simplified)
            all_strikes = sorted(set(calls['strike'].tolist() + puts['strike'].tolist()))
            net_flows = []

            for strike in all_strikes:
                call_flow = calls[calls['strike'] == strike]['volume'].sum() if not calls[calls['strike'] == strike].empty else 0
                put_flow = puts[puts['strike'] == strike]['volume'].sum() if not puts[puts['strike'] == strike].empty else 0
                net_flow = call_flow - put_flow
                net_flows.append(net_flow)

            colors = ['#60A5FA' if x >= 0 else '#EF4444' for x in net_flows]

            fig.add_trace(go.Bar(
                x=net_flows,
                y=all_strikes,
                orientation='h',
                name='Net Flow',
                marker_color=colors,
                opacity=0.8
            ))

        # Add current price line
        if current_price:
            fig.add_hline(y=current_price, line_dash="dash", line_color="white",
                         line_width=2, annotation_text=f"Current: ${current_price:.2f}",
                         annotation_position="top right")

        # Apply persistent zoom if available, otherwise set default zoom around current price
        zoom_key = f"{symbol}_{chart_id}"
        if zoom_key in st.session_state.chart_zoom_ranges:
            zoom_range = st.session_state.chart_zoom_ranges[zoom_key]
            fig.update_layout(
                xaxis=dict(range=zoom_range.get('x', [None, None])),
                yaxis=dict(range=zoom_range.get('y', [None, None]))
            )
        elif current_price:
            # Default zoom: ±8% around current price for Y-axis (tighter focus)
            price_range = current_price * 0.08
            default_y_range = [current_price - price_range, current_price + price_range]
            fig.update_layout(yaxis=dict(range=default_y_range))

        fig.update_layout(
            title=f"{symbol} - Net Flow by Strike",
            xaxis_title="Net Flow",
            yaxis_title="Strike Price",
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white'),
            showlegend=False,
            height=500,
            uirevision=zoom_key
        )

        fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='rgba(255,255,255,0.1)')
        fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='rgba(255,255,255,0.1)')

        return fig
    
    def calculate_greeks(self, options_data, current_price, risk_free_rate=0.05):
        """Calculate Greeks for options"""
        if options_data is None or options_data.empty:
            return options_data
        
        options_data = options_data.copy()
        
        # Time to expiration (assuming 30 days for simplification)
        T = 30 / 365.0
        
        for idx, row in options_data.iterrows():
            S = current_price
            K = row['strike']
            sigma = row.get('impliedVolatility', 0.2)
            r = risk_free_rate
            
            if sigma <= 0:
                sigma = 0.2  # Default volatility
            
            # Black-Scholes calculations
            d1 = (np.log(S/K) + (r + 0.5*sigma**2)*T) / (sigma*np.sqrt(T))
            d2 = d1 - sigma*np.sqrt(T)
            
            # Calculate Greeks
            if row.get('contractSymbol', '').endswith('C') or 'call' in str(row.get('contractSymbol', '')).lower():
                # Call option
                options_data.loc[idx, 'delta'] = norm.cdf(d1)
                options_data.loc[idx, 'gamma'] = norm.pdf(d1) / (S * sigma * np.sqrt(T))
                options_data.loc[idx, 'vanna'] = -norm.pdf(d1) * d2 / sigma
                options_data.loc[idx, 'charm'] = -norm.pdf(d1) * (r - (sigma**2)/2) / (sigma * np.sqrt(T))
            else:
                # Put option
                options_data.loc[idx, 'delta'] = -norm.cdf(-d1)
                options_data.loc[idx, 'gamma'] = norm.pdf(d1) / (S * sigma * np.sqrt(T))
                options_data.loc[idx, 'vanna'] = -norm.pdf(d1) * d2 / sigma
                options_data.loc[idx, 'charm'] = -norm.pdf(d1) * (r - (sigma**2)/2) / (sigma * np.sqrt(T))
        
        return options_data
    
    def create_dealer_pressure_map(self, calls, puts, current_price, symbol, greek_type='gamma'):
        """Create dealer pressure map heatmap"""
        if calls is None or puts is None or calls.empty or puts.empty:
            return go.Figure()
        
        # Calculate Greeks
        calls_with_greeks = self.calculate_greeks(calls, current_price)
        puts_with_greeks = self.calculate_greeks(puts, current_price)
        
        # Create price range for heatmap
        price_range = current_price * 0.15  # 15% range
        min_price = current_price - price_range
        max_price = current_price + price_range
        
        # Create grid
        price_points = np.linspace(min_price, max_price, 50)
        time_points = np.arange(0, 30, 1)  # 30 days
        
        # Initialize heatmap data
        heatmap_data = np.zeros((len(time_points), len(price_points)))
        
        # Calculate exposure for each point
        for i, days_to_exp in enumerate(time_points):
            for j, price_point in enumerate(price_points):
                total_exposure = 0
                
                # Process calls
                for _, call in calls_with_greeks.iterrows():
                    oi = call.get('openInterest', 0)
                    if oi > 0:
                        # Adjust Greek based on days to expiration and price point
                        greek_value = call.get(greek_type, 0)
                        exposure = greek_value * oi * 100  # Contract multiplier
                        
                        # Decay factor for time
                        decay_factor = np.exp(-days_to_exp * 0.1)
                        
                        # Distance factor for price
                        distance_factor = np.exp(-abs(price_point - call['strike']) / (current_price * 0.05))
                        
                        total_exposure += exposure * decay_factor * distance_factor
                
                # Process puts (negative exposure for dealer hedging)
                for _, put in puts_with_greeks.iterrows():
                    oi = put.get('openInterest', 0)
                    if oi > 0:
                        greek_value = put.get(greek_type, 0)
                        exposure = -greek_value * oi * 100  # Negative for puts
                        
                        decay_factor = np.exp(-days_to_exp * 0.1)
                        distance_factor = np.exp(-abs(price_point - put['strike']) / (current_price * 0.05))
                        
                        total_exposure += exposure * decay_factor * distance_factor
                
                heatmap_data[i, j] = total_exposure
        
        # Create heatmap
        fig = go.Figure(data=go.Heatmap(
            z=heatmap_data,
            x=price_points,
            y=time_points,
            colorscale=[
                [0, '#1e40af'],      # Strong blue
                [0.2, '#3b82f6'],    # Blue
                [0.4, '#60a5fa'],    # Light blue
                [0.5, '#000000'],    # Black (neutral)
                [0.6, '#f87171'],    # Light red
                [0.8, '#ef4444'],    # Red
                [1, '#dc2626']       # Strong red
            ],
            zmid=0,
            hoverongaps=False,
            hovertemplate='Price: $%{x:.2f}<br>Days: %{y}<br>Exposure: %{z:.0f}<extra></extra>'
        ))
        
        # Add current price line
        fig.add_vline(x=current_price, line_dash="dash", line_color="white", line_width=2)
        
        fig.update_layout(
            title=f"{symbol} - Dealer Pressure Map ({greek_type.upper()}×OI)",
            xaxis_title="Underlying Price",
            yaxis_title="Days to Expiration",
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white'),
            height=400
        )
        
        return fig
    
    def create_greek_profile_chart(self, calls, puts, current_price, symbol, greek_type='gamma', chart_id="greek_profile"):
        """Create Greek Profile chart with persistent zoom"""
        fig = go.Figure()

        if calls is not None and puts is not None and not calls.empty and not puts.empty:
            # Calculate Greeks
            calls_with_greeks = self.calculate_greeks(calls, current_price)
            puts_with_greeks = self.calculate_greeks(puts, current_price)

            # Combine all strikes
            all_strikes = sorted(set(calls_with_greeks['strike'].tolist() + puts_with_greeks['strike'].tolist()))
            greek_values = []

            for strike in all_strikes:
                call_greek = 0
                put_greek = 0

                # Get call Greek
                call_row = calls_with_greeks[calls_with_greeks['strike'] == strike]
                if not call_row.empty:
                    call_oi = call_row['openInterest'].iloc[0]
                    call_greek_val = call_row[greek_type].iloc[0] if greek_type in call_row.columns else 0
                    call_greek = call_greek_val * call_oi * 100

                # Get put Greek
                put_row = puts_with_greeks[puts_with_greeks['strike'] == strike]
                if not put_row.empty:
                    put_oi = put_row['openInterest'].iloc[0]
                    put_greek_val = put_row[greek_type].iloc[0] if greek_type in put_row.columns else 0
                    put_greek = -put_greek_val * put_oi * 100  # Negative for dealer hedging

                total_greek = call_greek + put_greek
                greek_values.append(total_greek)

            # Color based on positive/negative
            colors = ['#60A5FA' if x >= 0 else '#EF4444' for x in greek_values]

            fig.add_trace(go.Bar(
                x=greek_values,
                y=all_strikes,
                orientation='h',
                name=f'{greek_type.upper()} Profile',
                marker_color=colors,
                opacity=0.8
            ))

            # Add current price line
            fig.add_hline(y=current_price, line_dash="dash", line_color="white",
                         line_width=2, annotation_text=f"Current: ${current_price:.2f}",
                         annotation_position="top right")

        # Apply persistent zoom if available, otherwise set default zoom around current price
        zoom_key = f"{symbol}_{chart_id}_{greek_type}"
        if zoom_key in st.session_state.chart_zoom_ranges:
            zoom_range = st.session_state.chart_zoom_ranges[zoom_key]
            fig.update_layout(
                xaxis=dict(range=zoom_range.get('x', [None, None])),
                yaxis=dict(range=zoom_range.get('y', [None, None]))
            )
        elif current_price:
            # Default zoom: ±8% around current price for Y-axis (tighter focus)
            price_range = current_price * 0.08
            default_y_range = [current_price - price_range, current_price + price_range]
            fig.update_layout(yaxis=dict(range=default_y_range))

        fig.update_layout(
            title=f"{symbol} - {greek_type.upper()} Profile",
            xaxis_title=f"{greek_type.upper()} × OI",
            yaxis_title="Strike Price",
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white'),
            showlegend=False,
            height=400,
            uirevision=zoom_key
        )

        fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='rgba(255,255,255,0.1)')
        fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='rgba(255,255,255,0.1)')

        return fig

def main():
    dashboard = OptionsFlowDashboard()

    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🚀 Professional Options Flow Dashboard</h1>
        <p>Real-time multi-timeframe options flow analysis for SPX, ES, NQ, SPY, and QQQ</p>
    </div>
    """, unsafe_allow_html=True)

    # Controls
    col1, col2, col3, col4, col5 = st.columns([2, 1, 1, 1, 1])

    with col1:
        selected_symbol = st.selectbox(
            "Select Symbol",
            options=dashboard.symbols,
            format_func=lambda x: f"{x} - {dashboard.symbol_names.get(x, x)}"
        )

    with col2:
        greek_type = st.selectbox(
            "Greek Analysis",
            options=['gamma', 'delta', 'charm', 'vanna'],
            format_func=lambda x: f"{x.upper()}×OI"
        )

    with col3:
        refresh_interval = st.selectbox("Refresh Interval", [30, 60], index=0)

    with col4:
        auto_refresh = st.checkbox("Auto Refresh", value=st.session_state.auto_refresh)
        st.session_state.auto_refresh = auto_refresh

    with col5:
        if st.button("🔄 Refresh Now"):
            st.session_state.last_update = datetime.now()
            st.rerun()

    # Get available expiration dates
    with st.spinner(f"Fetching available expiration dates for {selected_symbol}..."):
        _, _, current_price, available_expirations = dashboard.fetch_options_data(selected_symbol)

    if not available_expirations:
        st.error(f"Unable to fetch expiration dates for {selected_symbol}")
        return

    # Expiration selection
    st.markdown("### 📅 Expiration Date Selection")
    col1, col2, col3 = st.columns([2, 2, 1])

    with col1:
        analysis_mode = st.radio(
            "Analysis Mode",
            ["Single Expiration", "Multiple Expirations"],
            horizontal=True
        )

    with col2:
        if analysis_mode == "Single Expiration":
            selected_expiration = st.selectbox(
                "Select Expiration Date",
                options=available_expirations,
                format_func=lambda x: f"{x} ({(datetime.strptime(x, '%Y-%m-%d') - datetime.now()).days} days)"
            )
            selected_expirations = [selected_expiration]
        else:
            selected_expirations = st.multiselect(
                "Select Multiple Expiration Dates",
                options=available_expirations[:5],  # Limit to first 5 for performance
                default=available_expirations[:2],  # Default to first 2
                format_func=lambda x: f"{x} ({(datetime.strptime(x, '%Y-%m-%d') - datetime.now()).days} days)"
            )

            if not selected_expirations:
                st.warning("Please select at least one expiration date")
                return

    with col3:
        multi_timeframe_view = st.checkbox("Multi-Timeframe View", value=False)
        if multi_timeframe_view and analysis_mode == "Single Expiration":
            st.info("Multi-timeframe view requires multiple expirations")
    
    # Auto refresh logic
    if auto_refresh:
        if (datetime.now() - st.session_state.last_update).seconds >= refresh_interval:
            st.session_state.last_update = datetime.now()
            st.rerun()
    
    # Display last update time
    st.markdown(f"**Last Updated:** {st.session_state.last_update.strftime('%H:%M:%S')}")
    
    # Fetch data based on analysis mode
    if analysis_mode == "Single Expiration":
        with st.spinner(f"Fetching options data for {selected_symbol} ({selected_expiration})..."):
            calls, puts, current_price, _ = dashboard.fetch_options_data(selected_symbol, selected_expiration)
    else:
        with st.spinner(f"Fetching multi-expiration options data for {selected_symbol}..."):
            calls, puts, current_price = dashboard.fetch_multiple_expirations_data(selected_symbol, selected_expirations)

    if calls is None or puts is None or (hasattr(calls, 'empty') and calls.empty):
        st.error(f"Unable to fetch options data for {selected_symbol}")
        return
    
    # Current price display
    if current_price:
        st.markdown(f"""
        <div class="metric-container">
            <h3>{selected_symbol} Current Price: ${current_price:.2f}</h3>
        </div>
        """, unsafe_allow_html=True)
    
    # Calculate exposure data
    calls_exp, puts_exp = dashboard.calculate_oi_weighted_exposure(calls, puts, current_price)
    
    # Create layout
    st.markdown("## 📊 Options Flow Analysis")
    
    # Top row - Main exposure charts
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown('<div class="chart-container">', unsafe_allow_html=True)
        oi_chart = dashboard.create_oi_exposure_chart(calls_exp, puts_exp, current_price, selected_symbol)
        st.plotly_chart(oi_chart, use_container_width=True)
        st.markdown('</div>', unsafe_allow_html=True)
    
    with col2:
        st.markdown('<div class="chart-container">', unsafe_allow_html=True)
        volume_chart = dashboard.create_volume_breakdown_chart(calls, puts, current_price, selected_symbol)
        st.plotly_chart(volume_chart, use_container_width=True)
        st.markdown('</div>', unsafe_allow_html=True)

    # Bottom row - Flow and Greek Profile
    col3, col4 = st.columns(2)

    with col3:
        st.markdown('<div class="chart-container">', unsafe_allow_html=True)
        flow_chart = dashboard.create_net_flow_chart(calls, puts, current_price, selected_symbol)
        st.plotly_chart(flow_chart, use_container_width=True)
        st.markdown('</div>', unsafe_allow_html=True)
    
    with col4:
        st.markdown('<div class="chart-container">', unsafe_allow_html=True)
        greek_profile_chart = dashboard.create_greek_profile_chart(calls, puts, current_price, selected_symbol, greek_type)
        st.plotly_chart(greek_profile_chart, use_container_width=True)
        st.markdown('</div>', unsafe_allow_html=True)
    
    # Dealer Pressure Map Section
    st.markdown("## 🔥 Dealer Pressure Analysis")
    
    # Full width dealer pressure maps
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown('<div class="chart-container">', unsafe_allow_html=True)
        st.markdown(f"### Dealer Pressure Map - {greek_type.upper()}×OI")
        pressure_map = dashboard.create_dealer_pressure_map(calls, puts, current_price, selected_symbol, greek_type)
        st.plotly_chart(pressure_map, use_container_width=True)
        st.markdown('</div>', unsafe_allow_html=True)
    
    with col2:
        st.markdown('<div class="metric-container">', unsafe_allow_html=True)
        st.markdown("### 📊 Analysis Legend")
        st.markdown("""
        **Color Coding:**
        - 🔴 **Red Areas**: Strong Sell Pressure  
        - 🔵 **Blue Areas**: Strong Buy Pressure  
        - ⚫ **Black Areas**: Neutral Zone  
        
        **Greek Explanations:**
        - **Gamma×OI**: Price acceleration risk
        - **Delta×OI**: Directional exposure  
        - **Charm×OI**: Time decay impact
        - **Vanna×OI**: Vol-price sensitivity
        
        **White Line**: Current underlying price
        """)
        st.markdown('</div>', unsafe_allow_html=True)
    
    # Data tables
    st.markdown("## 📋 Options Chain Data")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### Call Options")
        if not calls.empty:
            display_calls = calls[['strike', 'lastPrice', 'volume', 'openInterest', 'impliedVolatility']].copy()
            display_calls = display_calls.sort_values('volume', ascending=False).head(10)
            st.dataframe(display_calls, use_container_width=True)
    
    with col2:
        st.markdown("### Put Options")
        if not puts.empty:
            display_puts = puts[['strike', 'lastPrice', 'volume', 'openInterest', 'impliedVolatility']].copy()
            display_puts = display_puts.sort_values('volume', ascending=False).head(10)
            st.dataframe(display_puts, use_container_width=True)
    
    # Auto-refresh placeholder
    if auto_refresh:
        placeholder = st.empty()
        for seconds in range(refresh_interval, 0, -1):
            placeholder.markdown(f"⏱️ Next refresh in {seconds} seconds...")
            time.sleep(1)
        placeholder.empty()
        st.rerun()

if __name__ == "__main__":
    main()