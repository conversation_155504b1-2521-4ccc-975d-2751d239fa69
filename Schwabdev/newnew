"""
This file contains examples for stream requests.
"""

import ssl
import os
from dotenv import load_dotenv
import schwabdev
import logging

# Set logging level
logging.basicConfig(level=logging.DEBUG)

# Disable SSL verification (use only for development)
ssl._create_default_https_context = ssl._create_unverified_context

def main():
    # Load environment variables from .env file
    load_dotenv()

    # Create a Schwabdev client
    client = schwabdev.Client(os.getenv('app_key'), os.getenv('app_secret'), os.getenv('callback_url'))

    # Define a variable for the streamer
    streamer = client.stream

    # Example of using your own response handler
    def my_handler(message):
        print("test_handler:" + message)
    streamer.start(my_handler)

    # Stream data (example)
    streamer.send(streamer.level_one_equities("AMD,INTC", "0,1,2,3,4,5,6,7,8"))

    # Stop the stream after 60 seconds
    import time
    time.sleep(60)
    streamer.stop()


if __name__ == '__main__':
    print("Welcome to Schwabdev, The Unofficial Schwab API Python Wrapper!")
    print("Documentation: https://tylerebowers.github.io/Schwabdev/")
    main()