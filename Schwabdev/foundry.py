import streamlit as st
import yfinance as yf
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
from datetime import datetime, timedelta
import time
import warnings
warnings.filterwarnings('ignore')

# Page configuration
st.set_page_config(
    page_title="Options Flow Dashboard",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Custom CSS for dark theme matching the original
st.markdown("""
<style>
    .stApp {
        background-color: #0E1117;
        color: #FAFAFA;
    }
    
    .main-header {
        background: linear-gradient(90deg, #1f2937 0%, #374151 100%);
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
        border: 1px solid #374151;
    }
    
    .metric-container {
        background: #1f2937;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #374151;
    }
    
    .chart-container {
        background: #1f2937;
        border-radius: 0.5rem;
        border: 1px solid #374151;
        padding: 0.5rem;
    }
    
    div[data-testid="stMetricValue"] {
        color: #60A5FA;
    }
    
    .stSelectbox > div > div {
        background-color: #374151;
        color: #FAFAFA;
    }
    
    .stButton > button {
        background-color: #1f2937;
        color: #FAFAFA;
        border: 1px solid #374151;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'last_update' not in st.session_state:
    st.session_state.last_update = datetime.now()
if 'auto_refresh' not in st.session_state:
    st.session_state.auto_refresh = True

class OptionsFlowDashboard:
    def __init__(self):
        self.symbols = ['SPY', 'QQQ', '^SPX', 'ES=F', 'NQ=F']
        self.symbol_names = {
            'SPY': 'SPDR S&P 500 ETF',
            'QQQ': 'Invesco QQQ ETF',
            '^SPX': 'S&P 500 Index',
            'ES=F': 'E-mini S&P 500',
            'NQ=F': 'E-mini NASDAQ-100'
        }
        
    def fetch_options_data(self, symbol):
        """Fetch options data for a given symbol"""
        try:
            ticker = yf.Ticker(symbol)
            
            # Get current price
            hist = ticker.history(period="1d")
            current_price = hist['Close'].iloc[-1] if not hist.empty else None
            
            # Get options dates
            options_dates = ticker.options
            if not options_dates:
                return None, None, current_price
            
            # Get nearest expiration
            nearest_exp = options_dates[0]
            
            # Fetch options chain
            options_chain = ticker.option_chain(nearest_exp)
            calls = options_chain.calls
            puts = options_chain.puts
            
            return calls, puts, current_price
            
        except Exception as e:
            st.error(f"Error fetching data for {symbol}: {str(e)}")
            return None, None, None
    
    def calculate_oi_weighted_exposure(self, calls, puts, current_price):
        """Calculate Open Interest weighted exposure"""
        if calls is None or puts is None:
            return pd.DataFrame(), pd.DataFrame()
        
        # Filter for strikes within reasonable range
        price_range = current_price * 0.2  # 20% range
        min_strike = current_price - price_range
        max_strike = current_price + price_range
        
        calls_filtered = calls[(calls['strike'] >= min_strike) & (calls['strike'] <= max_strike)]
        puts_filtered = puts[(puts['strike'] >= min_strike) & (puts['strike'] <= max_strike)]
        
        # Calculate exposure (simplified gamma exposure approximation)
        calls_filtered = calls_filtered.copy()
        puts_filtered = puts_filtered.copy()
        
        # Approximate gamma exposure
        calls_filtered['exposure'] = calls_filtered['openInterest'] * calls_filtered['volume'].fillna(0) * 100
        puts_filtered['exposure'] = puts_filtered['openInterest'] * puts_filtered['volume'].fillna(0) * 100 * -1
        
        return calls_filtered, puts_filtered
    
    def create_oi_exposure_chart(self, calls, puts, current_price, symbol):
        """Create OI Weighted Exposure chart"""
        fig = go.Figure()
        
        if calls is not None and not calls.empty:
            fig.add_trace(go.Bar(
                x=calls['exposure'],
                y=calls['strike'],
                orientation='h',
                name='Call Impact',
                marker_color='#60A5FA',
                opacity=0.8
            ))
        
        if puts is not None and not puts.empty:
            fig.add_trace(go.Bar(
                x=puts['exposure'],
                y=puts['strike'],
                orientation='h',
                name='Put Impact',
                marker_color='#EF4444',
                opacity=0.8
            ))
        
        # Add current price line
        if current_price:
            fig.add_hline(y=current_price, line_dash="dash", line_color="white", 
                         annotation_text=f"Current: ${current_price:.2f}")
        
        fig.update_layout(
            title=f"{symbol} - OI Weighted Exposure",
            xaxis_title="Exposure",
            yaxis_title="Strike Price",
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white'),
            showlegend=True,
            height=500
        )
        
        fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='rgba(255,255,255,0.1)')
        fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='rgba(255,255,255,0.1)')
        
        return fig
    
    def create_volume_breakdown_chart(self, calls, puts, symbol):
        """Create Volume Bought and Sold chart"""
        fig = go.Figure()
        
        if calls is not None and not calls.empty:
            fig.add_trace(go.Bar(
                x=calls['volume'].fillna(0),
                y=calls['strike'],
                orientation='h',
                name='Call Volume',
                marker_color='#60A5FA',
                opacity=0.8
            ))
        
        if puts is not None and not puts.empty:
            fig.add_trace(go.Bar(
                x=puts['volume'].fillna(0) * -1,
                y=puts['strike'],
                orientation='h',
                name='Put Volume',
                marker_color='#EF4444',
                opacity=0.8
            ))
        
        fig.update_layout(
            title=f"{symbol} - Volume Breakdown",
            xaxis_title="Volume",
            yaxis_title="Strike Price",
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white'),
            showlegend=True,
            height=500
        )
        
        fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='rgba(255,255,255,0.1)')
        fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='rgba(255,255,255,0.1)')
        
        return fig
    
    def create_net_flow_chart(self, calls, puts, symbol):
        """Create Net Flow by Strike chart"""
        fig = go.Figure()
        
        if calls is not None and puts is not None and not calls.empty and not puts.empty:
            # Calculate net flow (simplified)
            all_strikes = sorted(set(calls['strike'].tolist() + puts['strike'].tolist()))
            net_flows = []
            
            for strike in all_strikes:
                call_flow = calls[calls['strike'] == strike]['volume'].sum() if not calls[calls['strike'] == strike].empty else 0
                put_flow = puts[puts['strike'] == strike]['volume'].sum() if not puts[puts['strike'] == strike].empty else 0
                net_flow = call_flow - put_flow
                net_flows.append(net_flow)
            
            colors = ['#60A5FA' if x >= 0 else '#EF4444' for x in net_flows]
            
            fig.add_trace(go.Bar(
                x=net_flows,
                y=all_strikes,
                orientation='h',
                name='Net Flow',
                marker_color=colors,
                opacity=0.8
            ))
        
        fig.update_layout(
            title=f"{symbol} - Net Flow by Strike",
            xaxis_title="Net Flow",
            yaxis_title="Strike Price",
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white'),
            showlegend=False,
            height=500
        )
        
        fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='rgba(255,255,255,0.1)')
        fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='rgba(255,255,255,0.1)')
        
        return fig
    
    def create_gamma_profile_chart(self, calls, puts, current_price, symbol):
        """Create Gamma Profile chart (simplified)"""
        fig = go.Figure()
        
        if calls is not None and puts is not None and not calls.empty and not puts.empty:
            # Simplified gamma calculation
            strikes = sorted(set(calls['strike'].tolist() + puts['strike'].tolist()))
            gamma_values = []
            
            for strike in strikes:
                # Simplified gamma approximation
                distance = abs(strike - current_price) / current_price
                gamma = np.exp(-distance * 10)  # Simplified gamma curve
                
                call_oi = calls[calls['strike'] == strike]['openInterest'].sum() if not calls[calls['strike'] == strike].empty else 0
                put_oi = puts[puts['strike'] == strike]['openInterest'].sum() if not puts[puts['strike'] == strike].empty else 0
                
                total_gamma = gamma * (call_oi + put_oi)
                gamma_values.append(total_gamma)
            
            fig.add_trace(go.Bar(
                x=gamma_values,
                y=strikes,
                orientation='h',
                name='Gamma Profile',
                marker_color='#10B981',
                opacity=0.8
            ))
        
        fig.update_layout(
            title=f"{symbol} - Gamma Profile",
            xaxis_title="Gamma",
            yaxis_title="Strike Price",
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white'),
            showlegend=False,
            height=400
        )
        
        fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='rgba(255,255,255,0.1)')
        fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='rgba(255,255,255,0.1)')
        
        return fig

def main():
    dashboard = OptionsFlowDashboard()
    
    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🚀 Professional Options Flow Dashboard</h1>
        <p>Real-time options flow analysis for SPX, ES, NQ, SPY, and QQQ</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Controls
    col1, col2, col3, col4 = st.columns([2, 1, 1, 1])
    
    with col1:
        selected_symbol = st.selectbox(
            "Select Symbol",
            options=dashboard.symbols,
            format_func=lambda x: f"{x} - {dashboard.symbol_names.get(x, x)}"
        )
    
    with col2:
        refresh_interval = st.selectbox("Refresh Interval", [30, 60], index=0)
    
    with col3:
        auto_refresh = st.checkbox("Auto Refresh", value=st.session_state.auto_refresh)
        st.session_state.auto_refresh = auto_refresh
    
    with col4:
        if st.button("🔄 Refresh Now"):
            st.session_state.last_update = datetime.now()
            st.rerun()
    
    # Auto refresh logic
    if auto_refresh:
        if (datetime.now() - st.session_state.last_update).seconds >= refresh_interval:
            st.session_state.last_update = datetime.now()
            st.rerun()
    
    # Display last update time
    st.markdown(f"**Last Updated:** {st.session_state.last_update.strftime('%H:%M:%S')}")
    
    # Fetch data
    with st.spinner(f"Fetching options data for {selected_symbol}..."):
        calls, puts, current_price = dashboard.fetch_options_data(selected_symbol)
    
    if calls is None or puts is None:
        st.error(f"Unable to fetch options data for {selected_symbol}")
        return
    
    # Current price display
    if current_price:
        st.markdown(f"""
        <div class="metric-container">
            <h3>{selected_symbol} Current Price: ${current_price:.2f}</h3>
        </div>
        """, unsafe_allow_html=True)
    
    # Calculate exposure data
    calls_exp, puts_exp = dashboard.calculate_oi_weighted_exposure(calls, puts, current_price)
    
    # Create layout
    st.markdown("## 📊 Options Flow Analysis")
    
    # Top row - Main exposure charts
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown('<div class="chart-container">', unsafe_allow_html=True)
        oi_chart = dashboard.create_oi_exposure_chart(calls_exp, puts_exp, current_price, selected_symbol)
        st.plotly_chart(oi_chart, use_container_width=True)
        st.markdown('</div>', unsafe_allow_html=True)
    
    with col2:
        st.markdown('<div class="chart-container">', unsafe_allow_html=True)
        volume_chart = dashboard.create_volume_breakdown_chart(calls, puts, selected_symbol)
        st.plotly_chart(volume_chart, use_container_width=True)
        st.markdown('</div>', unsafe_allow_html=True)
    
    # Bottom row - Flow and Gamma
    col3, col4 = st.columns(2)
    
    with col3:
        st.markdown('<div class="chart-container">', unsafe_allow_html=True)
        flow_chart = dashboard.create_net_flow_chart(calls, puts, selected_symbol)
        st.plotly_chart(flow_chart, use_container_width=True)
        st.markdown('</div>', unsafe_allow_html=True)
    
    with col4:
        st.markdown('<div class="chart-container">', unsafe_allow_html=True)
        gamma_chart = dashboard.create_gamma_profile_chart(calls, puts, current_price, selected_symbol)
        st.plotly_chart(gamma_chart, use_container_width=True)
        st.markdown('</div>', unsafe_allow_html=True)
    
    # Data tables
    st.markdown("## 📋 Options Chain Data")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### Call Options")
        if not calls.empty:
            display_calls = calls[['strike', 'lastPrice', 'volume', 'openInterest', 'impliedVolatility']].copy()
            display_calls = display_calls.sort_values('volume', ascending=False).head(10)
            st.dataframe(display_calls, use_container_width=True)
    
    with col2:
        st.markdown("### Put Options")
        if not puts.empty:
            display_puts = puts[['strike', 'lastPrice', 'volume', 'openInterest', 'impliedVolatility']].copy()
            display_puts = display_puts.sort_values('volume', ascending=False).head(10)
            st.dataframe(display_puts, use_container_width=True)
    
    # Auto-refresh placeholder
    if auto_refresh:
        placeholder = st.empty()
        for seconds in range(refresh_interval, 0, -1):
            placeholder.markdown(f"⏱️ Next refresh in {seconds} seconds...")
            time.sleep(1)
        placeholder.empty()
        st.rerun()

if __name__ == "__main__":
    main()