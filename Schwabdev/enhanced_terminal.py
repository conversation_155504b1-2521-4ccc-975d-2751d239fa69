#!/usr/bin/env python3
"""
Enhanced Terminal Dashboard with Interactive Visualizations
Combines terminal output with beautiful interactive graphs
"""
import yfinance as yf
import numpy as np
import pandas as pd
from datetime import datetime
import QuantLib as ql
import argparse
import time
import os
import sys

# Try to import plotly for interactive charts
try:
    import plotly.graph_objects as go
    import plotly.subplots as sp
    from plotly.offline import plot
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    print("⚠️  Install plotly for interactive charts: pip install plotly")

# Import our interactive visualizer
if PLOTLY_AVAILABLE:
    try:
        from .interactive_visualizer import InteractiveVisualizer
    except ImportError:
        try:
            from interactive_visualizer import InteractiveVisualizer
        except ImportError:
            print("⚠️  'interactive_visualizer' module not found. Interactive charts will be disabled.")
            InteractiveVisualizer = None
else:
    InteractiveVisualizer = None

def to_ql_date(date):
    """Convert datetime to QuantLib date"""
    return ql.Date(date.day, date.month, date.year)

def black_scholes_greeks(S, K, T, r, sigma, option_type="call"):
    """Calculate Black-Scholes Greeks using QuantLib"""
    try:
        evaluation_date = to_ql_date(datetime.now().date())
        maturity_date = evaluation_date + ql.Period(int(T * 365), ql.Days)
        
        ql.Settings.instance().evaluationDate = evaluation_date
        
        # Market data handles
        spot_handle = ql.QuoteHandle(ql.SimpleQuote(S))
        flat_ts = ql.YieldTermStructureHandle(
            ql.FlatForward(evaluation_date, r, ql.Actual365Fixed())
        )
        flat_vol_ts = ql.BlackVolTermStructureHandle(
            ql.BlackConstantVol(evaluation_date, ql.NullCalendar(), sigma, ql.Actual365Fixed())
        )
        
        # Black-Scholes process
        process = ql.BlackScholesProcess(spot_handle, flat_ts, flat_vol_ts)
        
        # Option setup
        payoff = ql.PlainVanillaPayoff(
            ql.Option.Call if option_type.lower() == "call" else ql.Option.Put, K
        )
        exercise = ql.EuropeanExercise(maturity_date)
        option = ql.VanillaOption(payoff, exercise)
        
        # Pricing engine
        engine = ql.AnalyticEuropeanEngine(process)
        option.setPricingEngine(engine)
        
        # Calculate Greeks
        delta = option.delta()
        gamma = option.gamma()
        vega = option.vega() / 100  # Per 1% vol change
        
        return delta, gamma, vega
        
    except Exception as e:
        return 0.0, 0.0, 0.0

def fetch_options_data(ticker="SPY", target_days=[7, 14, 30]):
    """Fetch options data with simple validation"""
    
    print(f"📊 Fetching {ticker} options data...")
    
    # Get ticker and spot price
    spy = yf.Ticker(ticker)
    spot_price = spy.history(period="1d")['Close'].iloc[-1]
    print(f"Current {ticker} price: ${spot_price:.2f}")
    
    # Get available expirations
    options = spy.options
    if not options:
        raise ValueError(f"No options available for {ticker}")
    
    print(f"Available expirations: {len(options)} dates")
    
    all_options = []
    
    for target in target_days:
        # Find closest expiration
        best_exp = None
        best_diff = float('inf')
        
        for exp_str in options:
            exp_date = datetime.strptime(exp_str, "%Y-%m-%d")
            days = (exp_date - datetime.now()).days
            diff = abs(days - target)
            
            if diff < best_diff and diff <= 3:  # Within 3 days
                best_diff = diff
                best_exp = (exp_str, exp_date, days)
        
        if not best_exp:
            print(f"⚠️  No expiration found for {target} days")
            continue
        
        exp_str, exp_date, actual_days = best_exp
        print(f"Using {exp_str} ({actual_days} days) for target {target} days")
        
        # Get option chain
        try:
            chain = spy.option_chain(exp_str)
            calls = chain.calls
            puts = chain.puts
            
            print(f"  Raw data: {len(calls)} calls, {len(puts)} puts")
            
            # Strike range (wide)
            strike_min = spot_price * 0.5
            strike_max = spot_price * 1.5
            
            valid_count = 0
            
            # Process calls and puts
            for options_df, option_type in [(calls, 'call'), (puts, 'put')]:
                for _, option in options_df.iterrows():
                    # Basic validation
                    if pd.isna(option.get('strike')) or option['strike'] <= 0:
                        continue
                        
                    if not (strike_min <= option['strike'] <= strike_max):
                        continue
                    
                    iv = option.get('impliedVolatility', np.nan)
                    if pd.isna(iv) or not (0.001 <= iv <= 10.0):
                        continue
                    
                    oi = option.get('openInterest', 0)
                    if pd.isna(oi):
                        oi = 0
                    
                    volume = option.get('volume', 0)
                    if pd.isna(volume):
                        volume = 0
                    
                    last_price = option.get('lastPrice', 0)
                    if pd.isna(last_price):
                        last_price = 0
                    
                    all_options.append({
                        'Strike': float(option['strike']),
                        'Type': option_type,
                        'OI': int(oi),
                        'Expiration': exp_date,
                        'T': actual_days / 365.0,
                        'IV': float(iv),
                        'Volume': int(volume),
                        'LastPrice': float(last_price)
                    })
                    valid_count += 1
            
            print(f"  Valid options: {valid_count}")
            
        except Exception as e:
            print(f"⚠️  Error fetching {exp_str}: {e}")
    
    if not all_options:
        raise ValueError("No valid options data found")
    
    df = pd.DataFrame(all_options)
    print(f"✅ Total valid options: {len(df)}")
    
    return df, spot_price

def calculate_greeks(data, spot_price, risk_free_rate=0.03):
    """Calculate Greeks for all options"""
    
    print(f"🧮 Calculating Greeks for {len(data)} options...")
    
    data['Delta'] = 0.0
    data['Gamma'] = 0.0
    data['Vanna'] = 0.0
    
    processed = 0
    for idx, row in data.iterrows():
        delta, gamma, vanna = black_scholes_greeks(
            spot_price, row['Strike'], row['T'], risk_free_rate, 
            row['IV'], row['Type']
        )
        
        data.at[idx, 'Delta'] = delta
        data.at[idx, 'Gamma'] = gamma
        data.at[idx, 'Vanna'] = vanna
        
        processed += 1
        if processed % 50 == 0:
            print(f"  Processed {processed}/{len(data)} options...")
    
    print(f"✅ Greeks calculated for {processed} options")
    return data

def calculate_gex_vex(data, spot_price):
    """Calculate GEX and VEX with corrected formulas"""
    
    print(f"💰 Calculating GEX and VEX...")
    
    # GEX: Gamma * OI * 100 * Spot^2 * 0.0001
    data['GEX'] = np.where(
        data['Type'] == 'call',
        data['Gamma'] * data['OI'] * 100 * (spot_price ** 2) * 0.0001,
        -data['Gamma'] * data['OI'] * 100 * (spot_price ** 2) * 0.0001
    )
    
    # VEX: Vanna * OI * 100 * Spot * 0.01
    data['VEX'] = np.where(
        data['Type'] == 'call',
        data['Vanna'] * data['OI'] * 100 * spot_price * 0.01,
        -data['Vanna'] * data['OI'] * 100 * spot_price * 0.01
    )
    
    total_gex = data['GEX'].sum()
    total_vex = data['VEX'].sum()
    gex_plus = total_gex + total_vex
    
    return total_gex, total_vex, gex_plus, data

def find_key_levels(data, spot_price):
    """Find important GEX levels"""
    
    # Sort by strike and calculate cumulative GEX
    strike_data = data.groupby('Strike').agg({
        'GEX': 'sum',
        'VEX': 'sum',
        'OI': 'sum'
    }).reset_index().sort_values('Strike')
    
    strike_data['Cumulative_GEX'] = strike_data['GEX'].cumsum()
    
    # Find zero GEX level
    zero_gex_level = None
    for i in range(1, len(strike_data)):
        if (strike_data.iloc[i-1]['Cumulative_GEX'] * strike_data.iloc[i]['Cumulative_GEX']) < 0:
            # Linear interpolation
            prev_gex = strike_data.iloc[i-1]['Cumulative_GEX']
            curr_gex = strike_data.iloc[i]['Cumulative_GEX']
            prev_strike = strike_data.iloc[i-1]['Strike']
            curr_strike = strike_data.iloc[i]['Strike']
            
            zero_gex_level = prev_strike + (curr_strike - prev_strike) * (-prev_gex / (curr_gex - prev_gex))
            break
    
    # Find max positive and negative levels
    max_pos_idx = strike_data['Cumulative_GEX'].idxmax()
    max_neg_idx = strike_data['Cumulative_GEX'].idxmin()
    
    return {
        'zero_gex_level': zero_gex_level,
        'max_positive_gex': {
            'strike': strike_data.loc[max_pos_idx, 'Strike'],
            'value': strike_data.loc[max_pos_idx, 'Cumulative_GEX']
        },
        'max_negative_gex': {
            'strike': strike_data.loc[max_neg_idx, 'Strike'], 
            'value': strike_data.loc[max_neg_idx, 'Cumulative_GEX']
        }
    }

def print_results(spot_price, risk_free_rate, total_gex, total_vex, gex_plus, 
                 data, execution_time, key_levels):
    """Print formatted results"""
    
    print("\
" + "="*70)
    print("📊 MARKET DATA")
    print("="*70)
    print(f"Spot Price:          ${spot_price:,.2f}")
    print(f"Risk-Free Rate:      {risk_free_rate:.4f} ({risk_free_rate*100:.2f}%)")
    print(f"Contracts Analyzed:  {len(data):,}")
    print(f"Execution Time:      {execution_time:.2f} seconds")
    print(f"Performance:         {len(data)/execution_time:.0f} contracts/sec")
    
    print("\
💰 EXPOSURE ANALYSIS")
    print("="*70)
    
    gex_color = "🟢" if total_gex > 0 else "🔴" if total_gex < 0 else "⚪"
    vex_color = "🟢" if total_vex > 0 else "🔴" if total_vex < 0 else "⚪"
    
    print(f"Total GEX:           {gex_color} ${total_gex:,.0f}")
    print(f"Total VEX:           {vex_color} ${total_vex:,.0f}")
    print(f"GEX+ (Combined):     🔵 ${gex_plus:,.0f}")
    
    # Key levels
    print("\
🎯 KEY LEVELS")
    print("="*70)
    
    if key_levels['zero_gex_level']:
        zero_level = key_levels['zero_gex_level']
        distance = ((zero_level - spot_price) / spot_price) * 100
        print(f"Zero GEX Level:      ${zero_level:.2f} ({distance:+.1f}% from spot)")
    
    max_pos = key_levels['max_positive_gex']
    print(f"Max Positive GEX:    ${max_pos['strike']:.2f} (${max_pos['value']:,.0f})")
    
    max_neg = key_levels['max_negative_gex'] 
    print(f"Max Negative GEX:    ${max_neg['strike']:.2f} (${max_neg['value']:,.0f})")
    
    # Additional breakdown
    calls = data[data['Type'] == 'call']
    puts = data[data['Type'] == 'put']
    
    print(f"\
Call GEX:            ${calls['GEX'].sum():,.0f}")
    print(f"Put GEX:             ${puts['GEX'].sum():,.0f}")
    print(f"Call VEX:            ${calls['VEX'].sum():,.0f}")
    print(f"Put VEX:             ${puts['VEX'].sum():,.0f}")

def create_interactive_charts(data, spot_price, total_gex, total_vex, gex_plus, ticker, chart_type="all"):
    """Create interactive charts if plotly is available"""

    if not PLOTLY_AVAILABLE:
        print("⚠️  Plotly not available. Install with: pip install plotly")
        return []

    if InteractiveVisualizer is None:
        print("⚠️  InteractiveVisualizer not available. Interactive charts will be skipped.")
        return []

    print(f"🎨 Creating interactive charts...")

    visualizer = InteractiveVisualizer()
    created_files = []
    
    try:
        # Validate data before creating charts
        if data is None or data.empty:
            print("❌ No data available for chart creation")
            return []

        print(f"📊 Data validation: {len(data)} rows, columns: {list(data.columns)}")

        # Check for required columns
        required_columns = ['Strike', 'GEX', 'VEX']
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            print(f"❌ Missing required columns: {missing_columns}")
            return []

        analysis_results = {
            'total_gex': total_gex,
            'total_vex': total_vex,
            'gex_plus': gex_plus
        }

        if chart_type == "all" or chart_type == "dashboard":
            # Create comprehensive dashboard
            print("🎨 Creating dashboard...")
            dashboard_file = visualizer.create_gex_vex_dashboard(
                data, spot_price, analysis_results, ticker
            )
            created_files.append(dashboard_file)
            print(f"✅ Interactive dashboard: {dashboard_file}")

        if chart_type == "all" or chart_type == "individual":
            # Create individual charts
            print("🎨 Creating individual charts...")
            gex_file = visualizer.create_single_chart(data, spot_price, "gex", ticker)
            vex_file = visualizer.create_single_chart(data, spot_price, "vex", ticker)
            gex_plus_file = visualizer.create_single_chart(data, spot_price, "gex_plus", ticker)

            created_files.extend([gex_file, vex_file, gex_plus_file])
            print(f"✅ Individual charts: {gex_file}, {vex_file}, {gex_plus_file}")

        print(f"📊 Charts will open automatically in your browser!")

    except Exception as e:
        print(f"❌ Error creating charts: {e}")
        print(f"❌ Error type: {type(e).__name__}")
        import traceback
        print(f"❌ Traceback: {traceback.format_exc()}")

    return created_files

def run_analysis(ticker="SPY", days=[7, 14, 30], export=False, charts="dashboard"):
    """Run complete analysis with interactive visualizations"""
    
    start_time = time.time()
    
    print("╔══════════════════════════════════════════════════════════════════╗")
    print("║                 ENHANCED OPTIONS DASHBOARD                       ║")
    print("║              With Interactive Visualizations                     ║")
    print("╚══════════════════════════════════════════════════════════════════╝")
    
    try:
        # Fetch data
        data, spot_price = fetch_options_data(ticker, days)
        
        # Get risk-free rate (simplified)
        risk_free_rate = 0.03  # Default for now
        
        # Calculate Greeks
        data = calculate_greeks(data, spot_price, risk_free_rate)
        
        # Calculate GEX/VEX
        total_gex, total_vex, gex_plus, data = calculate_gex_vex(data, spot_price)
        
        # Find key levels
        key_levels = find_key_levels(data, spot_price)
        
        execution_time = time.time() - start_time
        
        # Print results
        print_results(spot_price, risk_free_rate, total_gex, total_vex, 
                     gex_plus, data, execution_time, key_levels)
        
        # Create interactive charts
        chart_files = create_interactive_charts(
            data, spot_price, total_gex, total_vex, gex_plus, ticker, charts
        )
        
        # Export if requested
        if export:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{ticker}_analysis_{timestamp}.csv"
            data.to_csv(filename, index=False)
            print(f"\
💾 Data exported to {filename}")
        
        return True, chart_files
        
    except Exception as e:
        print(f"\
❌ Error: {e}")
        return False, []

def main():
    """Main function with command line arguments"""
    parser = argparse.ArgumentParser(description="Enhanced Options Dashboard with Interactive Charts")
    
    parser.add_argument("--ticker", "-t", default="SPY", help="Stock ticker")
    parser.add_argument("--days", "-d", nargs="+", type=int, default=[7, 14, 30], 
                       help="Days to expiration")
    parser.add_argument("--export", action="store_true", help="Export data to CSV")
    parser.add_argument("--charts", "-c", choices=["dashboard", "individual", "all", "none"], 
                       default="dashboard", help="Type of charts to create")
    parser.add_argument("--no-charts", action="store_true", help="Skip chart creation")
    
    args = parser.parse_args()
    
    # Override charts if no-charts is specified
    if args.no_charts:
        args.charts = "none"
    
    success, chart_files = run_analysis(args.ticker, args.days, args.export, args.charts)
    
    if success:
        print("\
🎉 Analysis completed successfully!")
        if chart_files:
            print(f"📊 Created {len(chart_files)} interactive chart(s)")
            print("💡 Charts should open automatically in your browser")
            print("💡 You can also open the HTML files manually")
    else:
        print("\
💥 Analysis failed!")

if __name__ == "__main__":
    main()