from deltaapi import OptionsMonitor

# Create an instance of OptionsMonitor
# You can customize the parameters:
# - symbol: The ticker symbol to monitor (default is "SPY")
# - strike_count: Number of strikes to fetch (default is 20)
# - max_points: Maximum number of data points to store (default is 100)
monitor = OptionsMonitor(symbol="SPY", strike_count=20, max_points=100)

# Start the monitoring dashboard
# The update_interval is in milliseconds (30000 = 30 seconds)
monitor.run(update_interval=30000)