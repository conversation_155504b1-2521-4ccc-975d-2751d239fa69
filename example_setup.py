import os
import sys

# Add the directory containing config.py to the Python path
# Replace with your actual path if needed
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from schwab_api import Client

# Create a new client
client = Client()

# Run setup process
if client.setup():
    print("Setup completed successfully!")
    # Test a simple API call
    quote = client.get_Quote("SPY")
    print(f"SPY quote: {quote}")
else:
    print("Setup failed. Check the error messages above.")
