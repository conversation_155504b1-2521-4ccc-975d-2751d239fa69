import dash
from dash import dcc, html, dash_table
import dash_bootstrap_components as dbc
import plotly.graph_objs as go
import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime, timedelta
import pickle
import os
from pathlib import Path

# Initialize cache file
CACHE_FILE = "options_cache.pkl"
CACHE_TIMEOUT = timedelta(minutes=15)

def load_cache(ticker):
    """Load cached options data if available and not expired."""
    if os.path.exists(CACHE_FILE):
        with open(CACHE_FILE, 'rb') as f:
            cache = pickle.load(f)
        if cache['ticker'] == ticker and datetime.now() - cache['timestamp'] < CACHE_TIMEOUT:
            return cache['data'], cache['price'], cache['expiry'], cache['current_price'], cache['all_options']
    return None, None, None, None, None

def save_cache(ticker, data, price, expiry, current_price, all_options):
    """Save options data to cache."""
    cache = {
        'ticker': ticker,
        'timestamp': datetime.now(),
        'data': data,
        'price': price,
        'expiry': expiry,
        'current_price': current_price,
        'all_options': all_options
    }
    with open(CACHE_FILE, 'wb') as f:
        pickle.dump(cache, f)

def fetch_options_data(ticker='^SPX', force_refresh=False):
    """Fetch options data with caching and improved error handling."""
    if not force_refresh:
        cached_data = load_cache(ticker)
        if cached_data[0] is not None:
            print(f"Using cached data for {ticker}")
            return cached_data

    try:
        stock = yf.Ticker(ticker)
        current_price = stock.history(period="1d")['Close'].iloc[-1]
        expirations = stock.options

        if not expirations:
            print(f"No expiration dates available for {ticker}")
            return pd.DataFrame(), [], None, 0, pd.DataFrame()

        expirations_to_fetch = expirations[:5]
        all_options = []

        for expiry in expirations_to_fetch:
            try:
                opt_chain = stock.option_chain(expiry)
                calls = opt_chain.calls.copy()
                calls['type'] = 'call'
                calls['expiry'] = expiry
                puts = opt_chain.puts.copy()
                puts['type'] = 'put'
                puts['expiry'] = expiry
                options = pd.concat([calls, puts])
                all_options.append(options)
            except Exception as e:
                print(f"Error fetching data for {expiry}: {e}")
                continue

        if not all_options:
            print("No options data retrieved")
            return pd.DataFrame(), [], None, 0, pd.DataFrame()

        options_data = pd.concat(all_options)

        # Rename columns
        column_mapping = {
            'contractSymbol': 'contract',
            'lastTradeDate': 'last_trade_date',
            'strike': 'strike',
            'lastPrice': 'last_price',
            'bid': 'bid',
            'ask': 'ask',
            'change': 'change',
            'percentChange': 'percent_change',
            'volume': 'volume',
            'openInterest': 'openInterest',
            'impliedVolatility': 'iv',
            'inTheMoney': 'in_the_money'
        }
        rename_dict = {old: new for old, new in column_mapping.items() if old in options_data.columns}
        options_data = options_data.rename(columns=rename_dict)

        # Handle missing strike column
        if 'strike' not in options_data.columns:
            def extract_strike(symbol):
                try:
                    strike_part = symbol[-8:]
                    strike = float(strike_part) / 1000
                    return strike
                except:
                    return np.nan
            options_data['strike'] = options_data['contract'].apply(extract_strike)

        # Handle missing openInterest
        if 'openInterest' not in options_data.columns:
            options_data['openInterest'] = options_data.get('volume', 100)

        # Calculate days to expiration
        options_data['expiry_date'] = pd.to_datetime(options_data['expiry'])
        options_data['dte'] = (options_data['expiry_date'] - pd.Timestamp.today().normalize()).dt.days

        # Save to cache
        save_cache(ticker, options_data, [], expirations[0], current_price, options_data)
        return options_data, [], expirations[0], current_price, options_data

    except Exception as e:
        print(f"Error fetching options data: {e}")
        return pd.DataFrame(), [], None, 0, pd.DataFrame()

def plot_dealer_oi_directional(options, current_price, selected_dte=0, use_volume=False):
    """Create a bar chart showing dealer directional exposure based on open interest or volume."""
    if options.empty:
        fig = go.Figure()
        fig.update_layout(
            title=dict(text="Error: No options data available", font=dict(size=18, color="#f8f9fa")),
            template="plotly_dark",
            plot_bgcolor='#1e1e1e',
            paper_bgcolor='#1e1e1e',
            font=dict(color='#f8f9fa'),
        )
        return fig

    # Filter by DTE if needed
    if selected_dte >= 0:
        filtered_options = options[options['dte'] == selected_dte]
        if filtered_options.empty:
            filtered_options = options
    else:
        filtered_options = options

    # Group by strike
    calls = filtered_options[filtered_options['type'] == 'call']
    puts = filtered_options[filtered_options['type'] == 'put']

    # Use volume or openInterest based on toggle
    metric = 'volume' if use_volume else 'openInterest'

    # Create DataFrame with all unique strikes
    all_strikes = pd.DataFrame({'strike': sorted(set(filtered_options['strike'].dropna().unique()))})

    # Calculate call exposure
    if not calls.empty:
        call_exposure = calls.groupby('strike')[metric].sum().reset_index()
        call_exposure.columns = ['strike', 'call_exposure']
        call_exposure['call_exposure'] = call_exposure['call_exposure'] * -1  # Dealers short calls
        all_strikes = pd.merge(all_strikes, call_exposure, on='strike', how='left')
    else:
        all_strikes['call_exposure'] = 0

    # Calculate put exposure
    if not puts.empty:
        put_exposure = puts.groupby('strike')[metric].sum().reset_index()
        put_exposure.columns = ['strike', 'put_exposure']
        all_strikes = pd.merge(all_strikes, put_exposure, on='strike', how='left')
    else:
        all_strikes['put_exposure'] = 0

    # Fill NaN values
    all_strikes = all_strikes.fillna(0)

    # Calculate net exposure
    all_strikes['net_exposure'] = all_strikes['call_exposure'] + all_strikes['put_exposure']

    # Identify support and resistance levels
    top_levels = all_strikes[abs(all_strikes['net_exposure']) > all_strikes['net_exposure'].quantile(0.9)]
    support_levels = top_levels[top_levels['net_exposure'] > 0]['strike'].tolist()
    resistance_levels = top_levels[top_levels['net_exposure'] < 0]['strike'].tolist()

    # Create figure
    fig = go.Figure()

    # Add bars with gradient colors
    for i, row in all_strikes.iterrows():
        strike = row['strike']
        exposure = row['net_exposure']
        is_support = strike in support_levels
        is_resistance = strike in resistance_levels
        color = '#00bc8c' if is_support else '#e74c3c' if is_resistance else 'rgba(0, 188, 140, 0.5)' if exposure >= 0 else 'rgba(231, 76, 60, 0.5)'
        border_width = 3 if is_support or is_resistance else 1

        fig.add_trace(go.Bar(
            x=[strike],
            y=[exposure],
            showlegend=False,
            marker=dict(
                color=color,
                line=dict(color='rgba(255, 255, 255, 0.8)', width=border_width)
            ),
            width=10,
            text=f"{int(exposure):,}" if is_support or is_resistance else None,
            textposition='auto',
            hovertemplate='Strike: %{x}<br>Exposure: %{y:,.0f}<extra></extra>'
        ))

    # Add current price line
    fig.add_vline(
        x=current_price,
        line_width=2,
        line_dash="dash",
        line_color="#3498db",
        annotation_text="Current Price",
        annotation_position="top right",
        annotation=dict(font=dict(color="#3498db", size=12))
    )

    # Add support/resistance lines
    for level in support_levels:
        fig.add_vline(x=level, line_width=1, line_dash="dot", line_color="#00bc8c",
                      annotation_text="Support", annotation_position="top left")
    for level in resistance_levels:
        fig.add_vline(x=level, line_width=1, line_dash="dot", line_color="#e74c3c",
                      annotation_text="Resistance", annotation_position="top left")

    # Update layout
    fig.update_layout(
        title=dict(text=f"Dealer Directional Exposure by Strike (DTE: {selected_dte if selected_dte >= 0 else 'All'})",
                   font=dict(size=18, color="#f8f9fa")),
        xaxis_title=dict(text="Strike Price", font=dict(size=14, color="#f8f9fa")),
        yaxis_title=dict(text=f"Dealer {'Volume' if use_volume else 'Open Interest'} Exposure", font=dict(size=14, color="#f8f9fa")),
        height=650,
        template="plotly_dark",
        plot_bgcolor='#1e1e1e',
        paper_bgcolor='#1e1e1e',
        font=dict(color='#f8f9fa'),
        margin=dict(l=40, r=40, t=60, b=80),
        barmode='relative',
        bargap=0.05,
    )

    # Add zero line
    fig.add_hline(y=0, line_width=1, line_dash="dash", line_color="rgba(255,255,255,0.3)")
    return fig

def plot_iv_skew(options, current_price, selected_dte=0):
    """Create a plot showing implied volatility skew across strikes."""
    if options.empty or 'iv' not in options.columns:
        fig = go.Figure()
        fig.update_layout(
            title=dict(text="Error: No IV data available", font=dict(size=18, color="#f8f9fa")),
            template="plotly_dark",
            plot_bgcolor='#1e1e1e',
            paper_bgcolor='#1e1e1e',
            font=dict(color='#f8f9fa'),
        )
        return fig

    filtered_options = options[options['dte'] == selected_dte] if selected_dte >= 0 else options
    calls = filtered_options[filtered_options['type'] == 'call']
    puts = filtered_options[filtered_options['type'] == 'put']

    fig = go.Figure()
    if not calls.empty:
        fig.add_trace(go.Scatter(
            x=calls['strike'], y=calls['iv'] * 100, mode='lines+markers', name='Calls',
            line=dict(color='#00bc8c'), marker=dict(size=8),
            hovertemplate='Strike: %{x}<br>IV: %{y:.2f}%<extra></extra>'
        ))
    if not puts.empty:
        fig.add_trace(go.Scatter(
            x=puts['strike'], y=puts['iv'] * 100, mode='lines+markers', name='Puts',
            line=dict(color='#e74c3c'), marker=dict(size=8),
            hovertemplate='Strike: %{x}<br>IV: %{y:.2f}%<extra></extra>'
        ))

    fig.add_vline(x=current_price, line_width=2, line_dash="dash", line_color="#3498db",
                  annotation_text="Current Price", annotation_position="top right")

    fig.update_layout(
        title=dict(text="Implied Volatility Skew", font=dict(size=18, color="#f8f9fa")),
        xaxis_title=dict(text="Strike Price", font=dict(size=14, color="#f8f9fa")),
        yaxis_title=dict(text="Implied Volatility (%)", font=dict(size=14, color="#f8f9fa")),
        height=400,
        template="plotly_dark",
        plot_bgcolor='#1e1e1e',
        paper_bgcolor='#1e1e1e',
        font=dict(color='#f8f9fa'),
        margin=dict(l=40, r=40, t=60, b=80),
    )
    return fig

def calculate_risk_reward(options, selected_strike, option_type, current_price):
    """Calculate risk-reward for a selected strike."""
    if options.empty or selected_strike not in options['strike'].values:
        return None
    option = options[(options['strike'] == selected_strike) & (options['type'] == option_type)]
    if option.empty:
        return None
    premium = option['last_price'].iloc[0]
    risk = premium * 100  # Per contract
    if option_type == 'call':
        reward = max(0, (selected_strike - current_price - premium) * 100)
    else:
        reward = max(0, (current_price - selected_strike - premium) * 100)
    rr_ratio = reward / risk if risk > 0 else 0
    return {'risk': risk, 'reward': reward, 'rr_ratio': rr_ratio}

# Dash application
app = dash.Dash(__name__, external_stylesheets=[dbc.themes.CYBORG])
app.title = "Dealer OI Directional Exposure"

app.layout = dbc.Container([
    html.H1("Dealer OI Directional Exposure", className="my-4 text-center", style={'color': '#f8f9fa'}),
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("Controls", className="bg-dark"),
                dbc.CardBody([
                    html.Label("Select Ticker:", className="mb-2"),
                    dcc.Input(id='ticker-input', value='^SPX', type='text', className="form-control mb-3"),
                    html.Label("Select Expiration:", className="mb-2"),
                    dcc.Dropdown(
                        id='dte-selector',
                        options=[
                            {'label': '0 DTE (Today)', 'value': 0},
                            {'label': '1 DTE', 'value': 1},
                            {'label': '2 DTE', 'value': 2},
                            {'label': '7 DTE', 'value': 7},
                            {'label': 'All Available', 'value': -1}
                        ],
                        value=0,
                        clearable=False,
                        className="mb-3"
                    ),
                    dcc.DatePickerSingle(
                        id='expiry-picker',
                        date=datetime.today(),
                        display_format='YYYY-MM-DD',
                        className="form-control mb-3"
                    ),
                    dcc.Checklist(
                        id='volume-toggle',
                        options=[{'label': 'Use Volume Instead of OI', 'value': 'volume'}],
                        value=[],
                        className="mb-3"
                    ),
                    dbc.Button('Refresh Data', id='refresh-button', color="success", className="w-100 mb-2"),
                    dbc.Button('Export to CSV', id='export-button', color="primary", className="w-100 mb-2"),
                    html.Label("Select Strike for Risk-Reward:", className="mb-2"),
                    dcc.Dropdown(id='strike-selector', className="mb-3"),
                    html.Div(id='rr-output', className="mt-2"),
                    html.Div(id='last-update-time', className="mt-2 text-center text-muted small")
                ])
            ])
        ], md=4),
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("Key Metrics", className="bg-dark"),
                dbc.CardBody([
                    dash_table.DataTable(
                        id='metrics-table',
                        columns=[
                            {'name': 'Strike', 'id': 'strike'},
                            {'name': 'Type', 'id': 'type'},
                            {'name': 'OI', 'id': 'openInterest'},
                            {'name': 'Volume', 'id': 'volume'},
                            {'name': 'IV (%)', 'id': 'iv'}
                        ],
                        style_table={'overflowX': 'auto'},
                        style_cell={'textAlign': 'left', 'backgroundColor': '#1e1e1e', 'color': '#f8f9fa'},
                        style_header={'backgroundColor': '#2c3e50', 'color': '#f8f9fa', 'fontWeight': 'bold'}
                    )
                ])
            ])
        ], md=8)
    ], className="mb-4"),
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("Dealer OI Directional Exposure", className="bg-dark"),
                dbc.CardBody([
                    dcc.Loading([
                        dcc.Graph(id='dealer-oi-graph')
                    ])
                ])
            ])
        ], md=12)
    ], className="mb-4"),
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardHeader("Implied Volatility Skew", className="bg-dark"),
                dbc.CardBody([
                    dcc.Loading([
                        dcc.Graph(id='iv-skew-graph')
                    ])
                ])
            ])
        ], md=12)
    ]),
    dcc.Interval(id='interval-component', interval=300*1000, n_intervals=0),
    dcc.Download(id="download-csv")
], fluid=True)

@app.callback(
    [
        dash.Output('dealer-oi-graph', 'figure'),
        dash.Output('iv-skew-graph', 'figure'),
        dash.Output('metrics-table', 'data'),
        dash.Output('last-update-time', 'children'),
        dash.Output('strike-selector', 'options'),
        dash.Output('rr-output', 'children')
    ],
    [
        dash.Input('ticker-input', 'value'),
        dash.Input('dte-selector', 'value'),
        dash.Input('expiry-picker', 'date'),
        dash.Input('volume-toggle', 'value'),
        dash.Input('refresh-button', 'n_clicks'),
        dash.Input('interval-component', 'n_intervals'),
        dash.Input('strike-selector', 'value')
    ]
)
def update_dashboard(ticker, selected_dte, expiry_date, volume_toggle, n_clicks, n_intervals, selected_strike):
    ctx = dash.callback_context
    force_refresh = ctx.triggered and ctx.triggered[0]['prop_id'].split('.')[0] == 'refresh-button'
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Fetch data
    options_data, price_data, expiry, current_price, all_options = fetch_options_data(ticker, force_refresh)

    if options_data.empty:
        return (
            plot_dealer_oi_directional(options_data, current_price),
            plot_iv_skew(options_data, current_price),
            [],
            f"Last updated: {current_time}",
            [],
            "No data available for risk-reward calculation"
        )

    # Filter by selected expiration date or DTE
    filtered_options = options_data
    if expiry_date:
        expiry_date = pd.to_datetime(expiry_date).strftime('%Y-%m-%d')
        filtered_options = options_data[options_data['expiry'] == expiry_date]
    elif selected_dte >= 0:
        filtered_options = options_data[options_data['dte'] == selected_dte]

    # Generate charts
    use_volume = 'volume' in volume_toggle
    dealer_fig = plot_dealer_oi_directional(filtered_options, current_price, selected_dte, use_volume)
    iv_fig = plot_iv_skew(filtered_options, current_price, selected_dte)

    # Prepare table data (top 5 strikes by OI)
    if not filtered_options.empty:
        table_data = filtered_options[['strike', 'type', 'openInterest', 'volume', 'iv']].copy()
        table_data['iv'] = table_data['iv'] * 100
        table_data = table_data.sort_values('openInterest', ascending=False).head(5).to_dict('records')
    else:
        table_data = []

    # Update strike selector options
    strike_options = [{'label': str(s), 'value': s} for s in sorted(filtered_options['strike'].dropna().unique())]

    # Calculate risk-reward for selected strike
    rr_text = "Select a strike to calculate risk-reward"
    if selected_strike:
        for opt_type in ['call', 'put']:
            rr = calculate_risk_reward(filtered_options, selected_strike, opt_type, current_price)
            if rr:
                rr_text = f"{opt_type.capitalize()} - Risk: ${rr['risk']:.2f}, Reward: ${rr['reward']:.2f}, R:R Ratio: {rr['rr_ratio']:.2f}"
                break

    return dealer_fig, iv_fig, table_data, f"Last updated: {current_time}", strike_options, rr_text

@app.callback(
    dash.Output("download-csv", "data"),
    dash.Input("export-button", "n_clicks"),
    dash.State("ticker-input", "value"),
    prevent_initial_call=True
)
def export_to_csv(n_clicks, ticker):
    options_data, _, _, _, _ = fetch_options_data(ticker)
    if not options_data.empty:
        return dcc.send_data_frame(options_data.to_csv, f"{ticker}_options_data.csv")
    return None

if __name__ == '__main__':
    app.run(debug=True, port=8051)