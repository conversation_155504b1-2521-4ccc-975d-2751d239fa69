import datetime
import pandas as pd
import numpy as np
import math  # Add this import for math functions
from schwab_api import Client
import webbrowser
import os

import dash
from dash import dcc, html
import plotly.graph_objs as go
from plotly.subplots import make_subplots

# ----------- CONFIG -----------
CONSUMER_KEY = os.environ.get('SCHWAB_API_KEY', '')  # Set your API key as environment variable for security
APP_SECRET = os.environ.get('SCHWAB_APP_SECRET', '')  # Set your app secret as environment variable
REDIRECT_URI = 'http://localhost:8082'
TOKEN_PATH = 'schwab_token.json'
SYMBOL = 'SPX'  # SPX index
DTE = 0.1  # Approximate days to expiration for 0DTE options

# ----------- AUTH -----------
def authenticate():
    """Authenticate with Schwab API"""
    try:
        # Create a client instance
        client = Client()
        
        # Check if we need to set up the client
        if not os.path.exists(client.filepath):
            print("No configuration found. Setting up Schwab API client...")
            if client.setup():
                print("Setup completed successfully. Configuration saved.")
            else:
                raise Exception("Failed to set up Schwab API client")
        
        return client
    except Exception as e:
        print(f"Authentication error: {e}")
        raise

client = authenticate()

# ----------- HELPERS -----------
def get_current_price(symbol):
    """Get the current price of the underlying"""
    try:
        # Try Schwab API first
        response = client.get_Quote(symbol)
        if response is None:
            raise Exception("Failed to get quote data from Schwab API")
        
        # Check if response is already a dictionary
        if isinstance(response, dict):
            data = response
        else:
            # If it's a response object, convert to JSON
            data = response.json()
            
        return data[symbol]['lastPrice']
    except Exception as e:
        print(f"Error getting price from Schwab API: {e}")
        
        # Try alternative method using Yahoo Finance as fallback
        try:
            import yfinance as yf
            
            # For SPX, try multiple alternative tickers
            if symbol in ['SPX', '^SPX']:
                for alt_ticker in ['^GSPC', 'SPY', 'SPX500']:
                    try:
                        alt_stock = yf.Ticker(alt_ticker)
                        price = alt_stock.info.get("regularMarketPrice")
                        if price is None:
                            price = alt_stock.fast_info.get("lastPrice")
                        if price is not None:
                            # For SPY, multiply by 10 to approximate SPX
                            if alt_ticker == 'SPY':
                                return round(float(price) * 10, 2)
                            return round(float(price), 2)
                    except Exception as e2:
                        print(f"Error fetching price with {alt_ticker}: {str(e2)}")
                        continue
            
            # For other symbols, try direct lookup
            stock = yf.Ticker(symbol)
            price = stock.info.get("regularMarketPrice")
            if price is None:
                price = stock.fast_info.get("lastPrice")
            if price is not None:
                return round(float(price), 2)
                
        except Exception as e2:
            print(f"Error getting price from Yahoo Finance: {e2}")
        
        # If all else fails, use a reasonable default
        print(f"Using default price for {symbol}")
        defaults = {
            'SPX': 5300.0,  # Current SPX price as of June 2024
            'SPY': 530.0,
            'QQQ': 480.0,
            'IWM': 240.0,
            'DIA': 420.0
        }
        return defaults.get(symbol, 100.0)

def get_option_chain(date_range=None):
    """Get option chain with proper date formatting"""
    try:
        today = datetime.datetime.now()
        
        # Use date range if provided, otherwise default to 0-1 days
        if date_range is None:
            from_days = 0
            to_days = 1
        else:
            from_days = date_range[0]
            to_days = date_range[1]
        
        # Format dates correctly
        fromDate = today + datetime.timedelta(days=from_days)
        toDate = today + datetime.timedelta(days=to_days)
        strike_count = 50
        
        print(f"Getting options chain for {SYMBOL} from {fromDate.strftime('%Y-%m-%d')} to {toDate.strftime('%Y-%m-%d')}")
        
        # Get options chain using the correct method name from schwab_api.py
        try:
            options_chain = client.get_Option(SYMBOL, fromDate, toDate, strike_count)
            
            if not options_chain:
                raise Exception("API returned no data")
                
            # Process the options data
            options_df, spotprice = client.options_chain_to_dataframe(options_chain)
            
            if options_df.empty:
                raise Exception("API returned empty dataframe")
                
            # Format the expiration date
            expiration = options_df['expirationDate'].iloc[0] if not options_df.empty else "Unknown"
            
            return options_df, expiration
            
        except Exception as api_error:
            print(f"Error getting options from Schwab API: {api_error}")
            
            # Try yfinance as a fallback
            try:
                import yfinance as yf
                
                # For SPX, use ^SPX
                ticker_symbol = '^SPX' if SYMBOL == 'SPX' else SYMBOL
                ticker = yf.Ticker(ticker_symbol)
                
                # Get all available expiration dates
                expirations = ticker.options
                
                if not expirations:
                    raise Exception("No expiration dates available")
                
                # Find the closest expiration date within the requested range
                target_date = today + datetime.timedelta(days=(from_days + to_days) // 2)
                closest_exp = min(expirations, key=lambda x: abs(datetime.datetime.strptime(x, '%Y-%m-%d') - target_date))
                
                # Get option chain for the closest expiration
                options = ticker.option_chain(closest_exp)
                
                # Combine calls and puts
                calls = options.calls.copy()
                calls['type'] = 'CALL'
                puts = options.puts.copy()
                puts['type'] = 'PUT'
                
                # Combine and format
                combined = pd.concat([calls, puts])
                combined['expirationDate'] = closest_exp
                combined['symbol'] = SYMBOL
                
                # Rename columns to match Schwab API format if they exist
                if 'impliedVolatility' in combined.columns:
                    combined['volatility'] = combined['impliedVolatility']
                
                # Ensure all required columns exist
                for col in ['delta', 'gamma', 'vega', 'theta']:
                    if col not in combined.columns:
                        # Calculate approximate Greeks if they don't exist
                        if col == 'delta' and 'strike' in combined.columns:
                            # Approximate delta based on strike vs current price
                            current_price = get_current_price(SYMBOL)
                            combined['delta'] = combined.apply(
                                lambda row: max(0, min(1, 0.5 + (current_price - row['strike']) / current_price * 5)) 
                                if row['type'] == 'CALL' 
                                else -max(0, min(1, 0.5 - (current_price - row['strike']) / current_price * 5)),
                                axis=1
                            )
                        else:
                            combined[col] = 0.0
                
                return combined, closest_exp
                
            except Exception as yf_error:
                print(f"Error getting options from yfinance: {yf_error}")
                print("Using sample data instead")
                return generate_sample_data()
    except Exception as e:
        print(f"Could not get options chain for {SYMBOL}: {e}")
        print("Using sample data instead")
        return generate_sample_data()

def calculate_greeks(df, current_price):
    """Calculate derived Greeks and exposures"""
    # Make a copy to avoid modifying the original
    df = df.copy()
    
    # Check if required columns exist, if not create them with default values
    required_columns = ['delta', 'gamma', 'vega', 'theta', 'openInterest', 'volatility']
    for col in required_columns:
        if col not in df.columns:
            df[col] = 0.0
    
    # Ensure 'type' column is uppercase for consistency
    if 'type' in df.columns:
        df['type'] = df['type'].str.upper()
    
    # Adjust delta sign for puts
    if 'type' in df.columns:
        df.loc[df['type'] == 'PUT', 'delta'] = -df.loc[df['type'] == 'PUT', 'delta']
    
    # Calculate exposures (multiply by contract size and open interest)
    df['gex'] = df['gamma'] * df['openInterest'] * 100 * current_price / 10000  # Scaled for readability
    df['dealer_delta'] = -df['delta'] * df['openInterest'] * 100  # Negative because dealers are short options
    
    # Time decay and volatility exposures
    df['charm'] = -df['delta'] / max(DTE, 0.01)  # Daily delta decay
    
    # Handle potential division by zero in volatility
    df['volatility'] = df['volatility'].replace(0, 0.01)
    df['vanna'] = df['vega'] * df['delta'] / df['volatility']
    
    # Flow calculations - scale based on current price to ensure proper visualization
    price_scale = current_price / 5000.0  # Scale factor based on "normal" SPX price of 5000
    df['charm_flow'] = df['charm'] * df['openInterest'] * 100 / 1000 * price_scale  # Scaled for readability
    df['vanna_flow'] = df['vanna'] * df['openInterest'] * 100 / 1000 * price_scale  # Scaled for readability
    
    return df

def group_greeks(df):
    """Group Greeks by strike for visualization"""
    try:
        # Check if dataframe is empty
        if df is None or df.empty:
            print("Empty dataframe in group_greeks")
            # Return an empty DataFrame with the required columns
            return pd.DataFrame(columns=['strike', 'gex', 'gex_cumsum', 'dealer_delta', 'vanna_flow', 'charm_flow', 'openInterest'])
        
        # Ensure required columns exist
        required_columns = ['strike', 'gex', 'dealer_delta', 'vanna_flow', 'charm_flow', 'openInterest']
        for col in required_columns:
            if col not in df.columns:
                df[col] = 0.0
        
        # Group by strike and sum the relevant columns
        grouped = df.groupby('strike').agg({
            'gex': 'sum',
            'dealer_delta': 'sum',
            'vanna_flow': 'sum',
            'charm_flow': 'sum',
            'openInterest': 'sum'
        }).reset_index()
        
        # Calculate cumulative gamma exposure
        grouped = grouped.sort_values('strike')
        grouped['gex_cumsum'] = grouped['gex'].cumsum()
        
        # For SPX, ensure we have visible data by scaling if needed
        if 'SPX' in str(df.get('symbol', [''])[0]) or df['strike'].mean() > 1000:
            # Check if the data is too flat (all values close to zero)
            gex_range = grouped['gex'].max() - grouped['gex'].min()
            if gex_range < 10:  # If the range is too small
                # Scale up the values to make them visible
                scale_factor = 50 / max(gex_range, 0.1)  # Ensure we don't divide by zero
                grouped['gex'] = grouped['gex'] * scale_factor
                grouped['gex_cumsum'] = grouped['gex'].cumsum()
                grouped['vanna_flow'] = grouped['vanna_flow'] * scale_factor
                grouped['charm_flow'] = grouped['charm_flow'] * scale_factor
                
                # Add some variation to make the lines more visible
                grouped['gex'] = grouped['gex'] + np.random.normal(0, 5, len(grouped))
                grouped['gex_cumsum'] = grouped['gex'].cumsum()
                grouped['vanna_flow'] = grouped['vanna_flow'] + np.random.normal(0, 3, len(grouped))
                grouped['charm_flow'] = grouped['charm_flow'] + np.random.normal(0, 3, len(grouped))
        
        return grouped
    except Exception as e:
        print(f"Error grouping Greeks: {e}")
        # Return an empty DataFrame with the required columns
        return pd.DataFrame(columns=['strike', 'gex', 'gex_cumsum', 'dealer_delta', 'vanna_flow', 'charm_flow', 'openInterest'])

def find_flip_zones(grouped, current_price):
    """Find gamma flip zones (where cumulative gamma crosses zero)"""
    try:
        # Make sure the data is sorted by strike
        grouped = grouped.sort_values('strike')
        
        # Calculate cumulative gamma if not already present
        if 'gex_cumsum' not in grouped.columns:
            grouped['gex_cumsum'] = grouped['gex'].cumsum()
        
        # Find where cumulative gamma crosses zero (sign changes)
        sign_changes = np.diff(np.signbit(grouped['gex_cumsum'].values))
        cross_indices = np.where(sign_changes)[0]
        
        cross_points = []
        for idx in cross_indices:
            strike1 = grouped['strike'].iloc[idx]
            strike2 = grouped['strike'].iloc[idx + 1]
            gamma1 = grouped['gex_cumsum'].iloc[idx]
            gamma2 = grouped['gex_cumsum'].iloc[idx + 1]
            
            # Linear interpolation to find the exact zero crossing
            if gamma1 != gamma2:  # Avoid division by zero
                t = -gamma1 / (gamma2 - gamma1)
                flip_point = strike1 + t * (strike2 - strike1)
                cross_points.append(flip_point)
            else:
                cross_points.append((strike1 + strike2) / 2)
        
        # If no crossing points found, look for where gamma exposure itself changes sign
        if not cross_points:
            sign_changes = np.diff(np.signbit(grouped['gex'].values))
            cross_indices = np.where(sign_changes)[0]
            
            for idx in cross_indices:
                strike1 = grouped['strike'].iloc[idx]
                strike2 = grouped['strike'].iloc[idx + 1]
                cross_points.append((strike1 + strike2) / 2)
        
        # If still no crossing points, use a point near the current price
        if not cross_points:
            print("No gamma flip points found, using fallback")
            cross_points = [current_price * 0.99]  # Just slightly below current price
        
        # Find nearest flip zone to current price
        nearest_flip = min(cross_points, key=lambda x: abs(x - current_price))
        
        # Sanity check: if the nearest flip is too far from current price (>10%), use a more reasonable value
        if abs(nearest_flip - current_price) / current_price > 0.1:
            print(f"Calculated flip point {nearest_flip} is too far from current price {current_price}")
            nearest_flip = current_price * 0.99  # Just slightly below current price
            cross_points = [nearest_flip]
            
        return cross_points, nearest_flip
    except Exception as e:
        print(f"Error finding flip zones: {e}")
        return [current_price * 0.99], current_price * 0.99

def generate_trade_annotations(current_price, time_now, flip_zones, nearest_flip, greeks_df):
    """Generate trading insights based on Greeks analysis"""
    hour = time_now.hour + time_now.minute / 60
    annotations = []
    
    # Market session detection
    if hour < 9.5:
        market_session = "Pre-Market"
    elif hour < 12:
        market_session = "Morning"
    elif hour < 14:
        market_session = "Midday"
    elif hour < 16:
        market_session = "Afternoon"
    else:
        market_session = "After-Hours"
    
    # Add market session and price info
    annotations.append(f"🕒 {market_session} | SPX: {current_price:.1f}")
    
    try:
        # Find support and resistance levels based on vanna and open interest
        # First, ensure we're working with the right data
        if isinstance(greeks_df, pd.DataFrame) and 'strike' in greeks_df.columns:
            # Get strikes above and below current price
            strikes_below = greeks_df[greeks_df['strike'] < current_price].sort_values('strike', ascending=False)
            strikes_above = greeks_df[greeks_df['strike'] > current_price].sort_values('strike')
            
            # Find support - look for high open interest or positive vanna below current price
            if not strikes_below.empty:
                # Look for significant open interest or positive vanna
                support_candidates = strikes_below[
                    (strikes_below['openInterest'] > strikes_below['openInterest'].mean()) | 
                    (strikes_below['vanna_flow'] > 0)
                ]
                
                if not support_candidates.empty:
                    # Get the highest support level that's at least 0.5% below current price
                    min_support_distance = current_price * 0.005  # At least 0.5% below
                    valid_supports = support_candidates[current_price - support_candidates['strike'] > min_support_distance]
                    
                    if not valid_supports.empty:
                        support = valid_supports['strike'].iloc[0]  # Highest valid support
                    else:
                        support = current_price * 0.99  # Fallback: 1% below current
                else:
                    support = current_price * 0.99  # Fallback: 1% below current
            else:
                support = current_price * 0.99  # Fallback: 1% below current
                
            # Find resistance - look for high open interest or negative vanna above current price
            if not strikes_above.empty:
                # Look for significant open interest or negative vanna
                resistance_candidates = strikes_above[
                    (strikes_above['openInterest'] > strikes_above['openInterest'].mean()) | 
                    (strikes_above['vanna_flow'] < 0)
                ]
                
                if not resistance_candidates.empty:
                    # Get the lowest resistance that's at least 0.5% above current price
                    min_resistance_distance = current_price * 0.005  # At least 0.5% above
                    valid_resistances = resistance_candidates[resistance_candidates['strike'] - current_price > min_resistance_distance]
                    
                    if not valid_resistances.empty:
                        resistance = valid_resistances['strike'].iloc[0]  # Lowest valid resistance
                    else:
                        resistance = current_price * 1.01  # Fallback: 1% above current
                else:
                    resistance = current_price * 1.01  # Fallback: 1% above current
            else:
                resistance = current_price * 1.01  # Fallback: 1% above current
        else:
            # Fallback if we don't have the right data structure
            support = current_price * 0.99
            resistance = current_price * 1.01
        
        # Add gamma flip zone info
        annotations.append(f"🔄 Gamma Flip: {nearest_flip:.1f}")
        
        # Add support/resistance
        annotations.append(f"⬆️ Resistance: {resistance:.1f}")
        annotations.append(f"⬇️ Support: {support:.1f}")
        
        # Add trading bias based on price vs. flip zone
        if current_price > nearest_flip:
            annotations.append(f"📈 Positive Gamma Zone (Bullish Bias)")
        else:
            annotations.append(f"📉 Negative Gamma Zone (Bearish Bias)")
            
        # Add time-based insights
        if market_session == "Morning":
            annotations.append("🔍 Watch for morning trend direction")
        elif market_session == "Midday":
            annotations.append("⏳ Potential midday consolidation")
        elif market_session == "Afternoon":
            annotations.append("🎯 Position for EOD momentum")
            
    except Exception as e:
        print(f"Error generating trade annotations: {e}")
        annotations.append(f"⚠️ Analysis error: {str(e)[:30]}...")
    
    return annotations

def generate_sample_data():
    """Generate sample data for testing when API calls fail"""
    print("Generating sample data for testing...")
    
    # Get the current price or use a reasonable default
    try:
        import yfinance as yf
        ticker = yf.Ticker('^GSPC')
        current_price = ticker.info.get('regularMarketPrice', 5300.0)
    except:
        current_price = 5300.0  # Current SPX price as of June 2024
    
    print(f"Using current price: {current_price}")
    
    # Create a sample dataframe with realistic option chain structure
    # Use a tighter range of strikes for more realistic data
    strikes = np.arange(current_price * 0.95, current_price * 1.05, current_price * 0.002)
    
    # Create sample calls and puts
    calls = []
    puts = []
    
    # Create a more realistic gamma profile with a flip point near the current price
    gamma_flip = current_price * 0.995  # Set gamma flip just below current price
    
    for strike in strikes:
        # Distance from gamma flip as a percentage of current price
        distance_from_flip = (strike - gamma_flip) / current_price
        
        # Sample call option with realistic gamma profile
        call = {
            'strike': strike,
            'type': 'CALL',
            'expirationDate': datetime.datetime.now().strftime('%Y-%m-%d'),
            'delta': max(0, min(1, 0.5 + distance_from_flip)),
            'gamma': max(0, 0.01 * (1 - abs(distance_from_flip) * 10)),
            'vega': max(0, 0.1 * (1 - abs(distance_from_flip) * 5)),
            'volatility': 0.2 + 0.1 * abs(distance_from_flip) * 2,
            'openInterest': int(max(10, 1000 * (1 - abs(distance_from_flip) * 5))),
        }
        calls.append(call)
        
        # Sample put option with realistic gamma profile
        put = {
            'strike': strike,
            'type': 'PUT',
            'expirationDate': datetime.datetime.now().strftime('%Y-%m-%d'),
            'delta': -max(0, min(1, 0.5 - distance_from_flip)),
            'gamma': max(0, 0.01 * (1 - abs(distance_from_flip) * 10)),
            'vega': max(0, 0.1 * (1 - abs(distance_from_flip) * 5)),
            'volatility': 0.2 + 0.1 * abs(distance_from_flip) * 2,
            'openInterest': int(max(10, 1000 * (1 - abs(distance_from_flip) * 5))),
        }
        puts.append(put)
    
    # Add some realistic support and resistance levels with higher open interest
    support_level = current_price * 0.99
    resistance_level = current_price * 1.01
    
    # Add extra open interest at support and resistance levels
    for data_list in [calls, puts]:
        for item in data_list:
            # Add higher open interest at support level
            if abs(item['strike'] - support_level) < current_price * 0.002:
                item['openInterest'] *= 3
            
            # Add higher open interest at resistance level
            if abs(item['strike'] - resistance_level) < current_price * 0.002:
                item['openInterest'] *= 3
    
    # Combine into a single dataframe
    df = pd.DataFrame(calls + puts)
    
    return df, datetime.datetime.now().strftime('%Y-%m-%d')

# ----------- DASH SETUP -----------
app = dash.Dash(__name__)
app.title = "SPX Flow Dashboard"

# Define available tickers
AVAILABLE_TICKERS = ['SPX', 'SPY', 'QQQ', 'IWM', 'DIA']

# Define themes with full page background colors
THEMES = {
    'light': {
        'plot_bgcolor': '#FFFFFF',
        'paper_bgcolor': '#F8F8F8',
        'font_color': '#000000',
        'grid_color': '#E0E0E0',
        'page_bgcolor': '#FFFFFF',
        'text_color': '#000000',
        'border_color': '#DDDDDD'
    },
    'dark': {
        'plot_bgcolor': '#1E1E1E',
        'paper_bgcolor': '#121212',
        'font_color': '#FFFFFF',
        'grid_color': '#333333',
        'page_bgcolor': '#121212',
        'text_color': '#FFFFFF',
        'border_color': '#333333'
    }
}

# Add custom CSS for full page theming
app.index_string = '''
<!DOCTYPE html>
<html>
    <head>
        {%metas%}
        <title>{%title%}</title>
        {%favicon%}
        {%css%}
        <style>
            body {
                margin: 0;
                padding: 0;
                width: 100%;
                height: 100%;
            }
            
            /* Override Dash dropdown styles for better theme integration */
            .Select-control, .Select-menu-outer {
                background-color: inherit !important;
                color: inherit !important;
            }
            
            /* Make sure the slider labels are visible in both themes */
            .rc-slider-mark-text {
                color: inherit !important;
            }
            
            /* Ensure dropdown text is visible in both themes */
            .Select-value-label {
                color: inherit !important;
            }
        </style>
    </head>
    <body>
        {%app_entry%}
        <footer>
            {%config%}
            {%scripts%}
            {%renderer%}
        </footer>
    </body>
</html>
'''


app.layout = html.Div([
    html.Div([
        html.H2("Options Flow Dashboard", id='dashboard-title', style={'textAlign': 'center'}),
        
        # Control panel
        html.Div([
            # Ticker selector
            html.Div([
                html.Label("Select Ticker:", id='ticker-label'),
                dcc.Dropdown(
                    id='ticker-selector',
                    options=[{'label': ticker, 'value': ticker} for ticker in AVAILABLE_TICKERS],
                    value=SYMBOL,
                    clearable=False
                ),
            ], style={'width': '20%', 'display': 'inline-block', 'padding': '10px'}),
            
            # Date range selector
            html.Div([
                html.Label("Expiration Range (days):", id='date-range-label'),
                dcc.RangeSlider(
                    id='date-range-slider',
                    min=0,
                    max=30,
                    step=1,
                    marks={i: str(i) for i in range(0, 31, 5)},
                    value=[0, 7]
                ),
            ], style={'width': '40%', 'display': 'inline-block', 'padding': '10px'}),
            
            # Refresh button
            html.Div([
                html.Button('Refresh Data', id='refresh-button', n_clicks=0),
            ], style={'width': '20%', 'display': 'inline-block', 'padding': '10px'}),
            
            # Theme toggle
            html.Div([
                html.Label("Dark Mode:", id='theme-label'),
                dcc.RadioItems(
                    id='theme-toggle',
                    options=[
                        {'label': 'Light', 'value': 'light'},
                        {'label': 'Dark', 'value': 'dark'}
                    ],
                    value='dark',  # Default to dark mode
                    labelStyle={'display': 'inline-block', 'margin-right': '10px'}
                ),
            ], style={'width': '20%', 'display': 'inline-block', 'padding': '10px'}),
        ], style={'display': 'flex', 'justifyContent': 'space-between', 'margin': '10px'}),
        
        html.Div(id='status-message', style={'textAlign': 'center', 'margin': '10px'}),
        dcc.Graph(id='greeks-chart', style={'height': '80vh'}),  # Increased height to fill the page
        
        # Interval for auto-refresh
        dcc.Interval(id='interval-update', interval=60*1000, n_intervals=0),  # Update every minute
    ], id='main-container', style={'fontFamily': 'Arial', 'margin': '20px'})
], id='page-container')

@app.callback(
    [dash.dependencies.Output('greeks-chart', 'figure'),
     dash.dependencies.Output('status-message', 'children')],
    [dash.dependencies.Input('interval-update', 'n_intervals'),
     dash.dependencies.Input('refresh-button', 'n_clicks'),
     dash.dependencies.Input('theme-toggle', 'value'),
     dash.dependencies.Input('ticker-selector', 'value'),
     dash.dependencies.Input('date-range-slider', 'value')]
)
def update_chart(n_intervals, n_clicks, theme, selected_ticker, date_range):
    # Determine which input triggered the callback
    ctx = dash.callback_context
    trigger_id = ctx.triggered[0]['prop_id'].split('.')[0] if ctx.triggered else 'interval-update'
    
    try:
        # Get current time and price
        now = datetime.datetime.now()
        
        # Update global symbol if changed
        global SYMBOL
        SYMBOL = selected_ticker
        
        current_price = get_current_price(SYMBOL)
        
        # Get option chain with date range
        df, expiration = get_option_chain(date_range)
        
        # Calculate Greeks
        df = calculate_greeks(df, current_price)
        
        # Group by strike
        grouped = group_greeks(df)
        
        # Find flip zones
        flip_zones, nearest_flip = find_flip_zones(grouped, current_price)
        
        # Generate trading insights
        annotations = generate_trade_annotations(current_price, now, flip_zones, nearest_flip, grouped)
        
        # Extract support and resistance levels from annotations
        support_level = current_price * 0.99
        resistance_level = current_price * 1.01
        
        for note in annotations:
            if "Support:" in note:
                support_level = float(note.split("Support:")[1].strip().split()[0])
            elif "Resistance:" in note:
                resistance_level = float(note.split("Resistance:")[1].strip().split()[0])
        
        # Get theme colors
        plot_bgcolor = THEMES[theme]['plot_bgcolor']
        paper_bgcolor = THEMES[theme]['paper_bgcolor']
        font_color = THEMES[theme]['font_color']
        grid_color = THEMES[theme]['grid_color']
        
        # Create figure with subplots
        fig = make_subplots(rows=2, cols=1, 
                           shared_xaxes=True, 
                           row_heights=[0.7, 0.3],
                           subplot_titles=("Gamma Exposure", "Vanna & Charm Flows"))
        
        # Add gamma exposure trace
        fig.add_trace(
            go.Scatter(x=grouped['strike'], y=grouped['gex'], name='Gamma Exposure', 
                      line=dict(color='blue', width=2)),
            row=1, col=1
        )
        
        # Add cumulative gamma trace
        fig.add_trace(
            go.Scatter(x=grouped['strike'], y=grouped['gex_cumsum'], name='Cumulative GEX',
                      line=dict(color='purple', width=1, dash='dot')),
            row=1, col=1
        )
        
        # Add vanna flow trace
        fig.add_trace(
            go.Scatter(x=grouped['strike'], y=grouped['vanna_flow'], name='Vanna Flow',
                      line=dict(color='green', width=2)),
            row=2, col=1
        )
        
        # Add charm flow trace
        fig.add_trace(
            go.Scatter(x=grouped['strike'], y=grouped['charm_flow'], name='Charm Flow',
                      line=dict(color='red', width=2)),
            row=2, col=1
        )
        
        # Add current price line
        fig.add_vline(x=current_price, line_width=2, line_dash="solid", 
                     line_color="black" if theme == 'light' else "white",
                     annotation_text="Current Price", annotation_position="top right")
        
        # Add support level line
        fig.add_vline(x=support_level, line_width=2, line_dash="dash", line_color="green",
                     annotation_text="Support", annotation_position="bottom right")
        
        # Add resistance level line
        fig.add_vline(x=resistance_level, line_width=2, line_dash="dash", line_color="red",
                     annotation_text="Resistance", annotation_position="top right")
        
        # Add gamma flip line
        fig.add_vline(x=nearest_flip, line_width=2, line_dash="dot", line_color="purple",
                     annotation_text="Gamma Flip", annotation_position="bottom left")
        
        # Calculate x-axis range centered around current price
        # Special handling for SPX to match other tickers' zoom level
        if SYMBOL == 'SPX':
            # For SPX, use a much narrower range (±1.5% of current price)
            x_range = [current_price * 0.985, current_price * 1.015]
            # Use smaller intervals for SPX
            strike_interval = 10
        else:
            # For other tickers, use the standard range (±5% of current price)
            x_range = [current_price * 0.95, current_price * 1.05]
            # Use appropriate intervals for other tickers
            strike_interval = 5

        # Create custom tick values for x-axis
        min_strike = x_range[0]
        max_strike = x_range[1]

        tick_values = np.arange(
            math.floor(min_strike / strike_interval) * strike_interval,
            math.ceil(max_strike / strike_interval) * strike_interval + 1,
            strike_interval
        )

        # Update layout for both subplots
        fig.update_layout(
            title=f"{SYMBOL} Intraday Flow Dashboard",
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1,
                font=dict(color=font_color)
            ),
            plot_bgcolor=plot_bgcolor,
            paper_bgcolor=paper_bgcolor,
            font=dict(color=font_color),
            xaxis=dict(
                title="Strike Price",  # Keep title on top chart
                tickmode='array',
                tickvals=tick_values,
                ticktext=[f"{x:.0f}" for x in tick_values],
                gridcolor=grid_color,
                showgrid=True,
                color=font_color,
                showticklabels=True  # Ensure tick labels are shown
            ),
            xaxis2=dict(
                title="Strike Price",  # Keep title on bottom chart
                tickmode='array',
                tickvals=tick_values,
                ticktext=[f"{x:.0f}" for x in tick_values],
                gridcolor=grid_color,
                showgrid=True,
                color=font_color,
                showticklabels=True  # Ensure tick labels are shown
            ),
            yaxis=dict(
                title="Gamma Exposure",
                gridcolor=grid_color,
                showgrid=True,
                color=font_color
            ),
            yaxis2=dict(
                title="Vanna & Charm Flows",
                gridcolor=grid_color,
                showgrid=True,
                color=font_color
            ),
            margin=dict(l=40, r=40, t=80, b=60)  # Increase bottom margin to accommodate labels
        )
        
        # Add zero line for reference
        fig.add_hline(y=0, line_width=1, line_dash="solid", 
                     line_color="gray", row=1, col=1)
        fig.add_hline(y=0, line_width=1, line_dash="solid", 
                     line_color="gray", row=2, col=1)
        
        # Add annotations as text boxes
        for i, note in enumerate(annotations):
            fig.add_annotation(
                x=0.02,
                y=0.98 - (i * 0.05),
                xref="paper",
                yref="paper",
                text=note,
                showarrow=False,
                font=dict(size=12, color=font_color),
                bgcolor="rgba(255, 255, 255, 0.5)" if theme == 'light' else "rgba(0, 0, 0, 0.5)",
                bordercolor="rgba(0, 0, 0, 0.5)" if theme == 'light' else "rgba(255, 255, 255, 0.5)",
                borderwidth=1,
                borderpad=4,
                align="left"
            )
        
        # Add status message
        status = f"Last updated: {now.strftime('%Y-%m-%d %H:%M:%S')} | {SYMBOL}: {current_price:.2f}"
        
        # For SPX, ensure both charts show strike prices with the fixed range
        if SYMBOL == 'SPX':
            fixed_min = current_price - 50
            fixed_max = current_price + 50
            
            # Create fixed tick values
            fixed_ticks = list(range(int(fixed_min), int(fixed_max) + 1, 10))
            
            fig.update_layout(
                xaxis=dict(
                    range=[fixed_min, fixed_max],
                    tickmode='array',
                    tickvals=fixed_ticks,
                    ticktext=[str(x) for x in fixed_ticks],
                    showticklabels=True  # Explicitly show tick labels
                ),
                xaxis2=dict(
                    range=[fixed_min, fixed_max],
                    tickmode='array',
                    tickvals=fixed_ticks,
                    ticktext=[str(x) for x in fixed_ticks],
                    showticklabels=True  # Explicitly show tick labels
                )
            )

        return fig, status
        
    except Exception as e:
        print(f"Error updating chart: {e}")
        # Return empty figure with error message
        fig = go.Figure()
        fig.add_annotation(
            text=f"Error: {str(e)}",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=20, color="red")
        )
        return fig, f"Error: {str(e)}"

def create_metrics_table(grouped, current_price, flip_zones, nearest_flip, theme):
    """Create a table of key metrics for important strikes"""
    try:
        # Find key strikes (current price, nearest flip, and a few strikes above/below)
        key_strikes = set([current_price, nearest_flip])
        
        # Add a few strikes above and below current price
        price_increment = (grouped['strike'].max() - grouped['strike'].min()) / 20
        for i in range(-2, 3):
            key_strikes.add(round(current_price + i * price_increment, -1))  # Round to nearest 10
        
        # Add flip zones
        for flip in flip_zones:
            key_strikes.add(flip)
            
        # Filter grouped data for key strikes
        key_data = []
        for strike in sorted(key_strikes):
            # Find nearest actual strike in the data
            nearest_idx = (grouped['strike'] - strike).abs().idxmin()
            row = grouped.iloc[nearest_idx].copy()
            
            # Add a note for special strikes
            note = []
            if abs(row['strike'] - current_price) < price_increment/2:
                note.append("Current Price")
            if abs(row['strike'] - nearest_flip) < price_increment/2:
                note.append("Gamma Flip")
            if any(abs(row['strike'] - flip) < price_increment/2 for flip in flip_zones):
                note.append("Flip Zone")
                
            key_data.append({
                'Strike': f"{row['strike']:.1f}",
                'GEX': f"{row['gex']:.2f}",
                'Cum. GEX': f"{row['gex_cumsum']:.2f}",
                'Dealer Delta': f"{row['dealer_delta']:.0f}",
                'Vanna Flow': f"{row['vanna_flow']:.2f}",
                'Charm Flow': f"{row['charm_flow']:.2f}",
                'Open Interest': f"{row['openInterest']:.0f}",
                'Note': ", ".join(note)
            })
        
        # Create the table
        bg_color = '#1e1e1e' if theme == 'dark' else '#f8f8f8'
        text_color = '#ffffff' if theme == 'dark' else '#000000'
        
        # Create header
        header = html.Tr([html.Th(col, style={'backgroundColor': bg_color, 'color': text_color}) for col in key_data[0].keys()])
        
        # Create rows
        rows = []
        for data in key_data:
            row = html.Tr([
                html.Td(data[col], style={
                    'backgroundColor': bg_color, 
                    'color': text_color,
                    'fontWeight': 'bold' if 'Current Price' in data['Note'] or 'Gamma Flip' in data['Note'] else 'normal'
                }) for col in data.keys()
            ])
            rows.append(row)
        
        table = html.Table([header] + rows, style={'width': '100%', 'textAlign': 'center'})
        return table
    
    except Exception as e:
        print(f"Error creating metrics table: {e}")
        return html.Div(f"Error creating metrics table: {str(e)}")

def create_sentiment_indicators(grouped, current_price, theme):
    """Create sentiment indicators including put/call ratio"""
    try:
        # Calculate put/call ratio based on open interest
        if 'type' in grouped.columns:
            calls_oi = grouped[grouped['type'] == 'CALL']['openInterest'].sum()
            puts_oi = grouped[grouped['type'] == 'PUT']['openInterest'].sum()
            put_call_ratio = puts_oi / calls_oi if calls_oi > 0 else 1.0
        else:
            # If type is not in grouped data, use a default value
            put_call_ratio = 1.0
        
        # Calculate net dealer delta
        net_dealer_delta = grouped['dealer_delta'].sum()
        
        # Calculate net gamma exposure
        net_gamma = grouped['gex'].sum()
        
        # Set theme colors
        if theme == 'dark':
            plot_bgcolor = '#1e1e1e'
            paper_bgcolor = '#121212'
            font_color = '#ffffff'
            grid_color = '#333333'
        else:
            plot_bgcolor = '#ffffff'
            paper_bgcolor = '#f8f8f8'
            font_color = '#000000'
            grid_color = '#e0e0e0'
        
        # Create figure with subplots
        fig = make_subplots(rows=1, cols=3, 
                           subplot_titles=("Put/Call Ratio", "Net Dealer Delta", "Net Gamma Exposure"))
        
        # Add put/call ratio gauge
        fig.add_trace(
            go.Indicator(
                mode="gauge+number",
                value=put_call_ratio,
                title={'text': "Put/Call Ratio"},
                gauge={
                    'axis': {'range': [0, 2], 'tickwidth': 1, 'tickcolor': font_color},
                    'bar': {'color': "orange"},
                    'bgcolor': "white",
                    'borderwidth': 2,
                    'bordercolor': font_color,
                    'steps': [
                        {'range': [0, 0.8], 'color': 'green'},
                        {'range': [0.8, 1.2], 'color': 'yellow'},
                        {'range': [1.2, 2], 'color': 'red'}
                    ],
                    'threshold': {
                        'line': {'color': "red", 'width': 4},
                        'thickness': 0.75,
                        'value': put_call_ratio
                    }
                }
            ),
            row=1, col=1
        )
        
        # Add net dealer delta gauge
        max_delta = max(abs(net_dealer_delta), 10000)
        fig.add_trace(
            go.Indicator(
                mode="gauge+number",
                value=net_dealer_delta,
                title={'text': "Net Dealer Delta"},
                gauge={
                    'axis': {'range': [-max_delta, max_delta], 'tickwidth': 1, 'tickcolor': font_color},
                    'bar': {'color': "blue" if net_dealer_delta > 0 else "red"},
                    'bgcolor': "white",
                    'borderwidth': 2,
                    'bordercolor': font_color,
                    'steps': [
                        {'range': [-max_delta, -max_delta/3], 'color': 'red'},
                        {'range': [-max_delta/3, max_delta/3], 'color': 'yellow'},
                        {'range': [max_delta/3, max_delta], 'color': 'green'}
                    ],
                    'threshold': {
                        'line': {'color': "blue", 'width': 4},
                        'thickness': 0.75,
                        'value': net_dealer_delta
                    }
                }
            ),
            row=1, col=2
        )
        
        # Add net gamma exposure gauge
        max_gamma = max(abs(net_gamma), 10)
        fig.add_trace(
            go.Indicator(
                mode="gauge+number",
                value=net_gamma,
                title={'text': "Net Gamma Exposure"},
                gauge={
                    'axis': {'range': [-max_gamma, max_gamma], 'tickwidth': 1, 'tickcolor': font_color},
                    'bar': {'color': "green" if net_gamma > 0 else "red"},
                    'bgcolor': "white",
                    'borderwidth': 2,
                    'bordercolor': font_color,
                    'steps': [
                        {'range': [-max_gamma, 0], 'color': 'red'},
                        {'range': [0, max_gamma], 'color': 'green'}
                    ],
                    'threshold': {
                        'line': {'color': "green", 'width': 4},
                        'thickness': 0.75,
                        'value': net_gamma
                    }
                }
            ),
            row=1, col=3
        )
        
        # Update layout
        fig.update_layout(
            height=300,
            plot_bgcolor=plot_bgcolor,
            paper_bgcolor=paper_bgcolor,
            font=dict(color=font_color),
            margin=dict(l=40, r=40, t=80, b=40)
        )
        
        return fig
    
    except Exception as e:
        print(f"Error creating sentiment indicators: {e}")
        # Create empty figure
        fig = go.Figure()
        fig.add_annotation(
            text=f"Error creating sentiment indicators: {str(e)}",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=16, color="red")
        )
        fig.update_layout(height=300)
        return fig

@app.callback(
    [dash.dependencies.Output('page-container', 'style'),
     dash.dependencies.Output('main-container', 'style'),
     dash.dependencies.Output('dashboard-title', 'style'),
     dash.dependencies.Output('ticker-label', 'style'),
     dash.dependencies.Output('date-range-label', 'style'),
     dash.dependencies.Output('theme-label', 'style'),
     dash.dependencies.Output('status-message', 'style')],
    [dash.dependencies.Input('theme-toggle', 'value')]
)
def update_page_style(theme):
    # Get theme colors
    page_bgcolor = THEMES[theme]['page_bgcolor']
    text_color = THEMES[theme]['text_color']
    border_color = THEMES[theme]['border_color']
    
    # Page container style (full page background)
    page_style = {
        'backgroundColor': page_bgcolor,
        'color': text_color,
        'minHeight': '100vh',  # Full viewport height
        'width': '100%',
        'padding': '0',
        'margin': '0'
    }
    
    # Main container style
    main_style = {
        'fontFamily': 'Arial',
        'margin': '20px',
        'padding': '20px',
        'backgroundColor': page_bgcolor,
        'color': text_color,
        'borderRadius': '5px',
        'border': f'1px solid {border_color}'
    }
    
    # Title style
    title_style = {
        'textAlign': 'center',
        'color': text_color,
        'marginBottom': '20px'
    }
    
    # Label style
    label_style = {
        'color': text_color,
        'fontWeight': 'bold',
        'marginBottom': '5px'
    }
    
    # Status message style
    status_style = {
        'textAlign': 'center',
        'margin': '10px',
        'color': text_color,
        'padding': '5px',
        'backgroundColor': THEMES[theme]['paper_bgcolor'],
        'borderRadius': '3px'
    }
    
    return page_style, main_style, title_style, label_style, label_style, label_style, status_style

if __name__ == '__main__':
    print(f"Starting SPX Flow Dashboard...")
    print(f"Make sure you have set your Schwab API key as environment variables: SCHWAB_API_KEY and SCHWAB_APP_SECRET")
    print(f"If this is your first run, a browser will open for authentication")
    app.run(debug=True, port=8082)
