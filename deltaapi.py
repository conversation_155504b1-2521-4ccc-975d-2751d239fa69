import time
from datetime import datetime, timedelta, timezone
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
import matplotlib.dates as mdates
from collections import deque
import pytz
import numpy as np
import pandas as pd
from typing import Tuple, Dict, Any, Optional
import logging
from schwab_api import Client  # Changed from client import Client

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OptionsMonitor:
    def __init__(self, symbol: str = "SPY", strike_count: int = 20, max_points: int = 100):
        self.client = Client()
        self.symbol = symbol
        self.strike_count = strike_count
        self.max_points = max_points
        
        # Data storage
        self.timestamps = deque(maxlen=max_points)
        self.call_deltas = deque(maxlen=max_points)
        self.put_deltas = deque(maxlen=max_points)
        self.net_deltas = deque(maxlen=max_points)
        self.delta_ratio = deque(maxlen=max_points)
        self.call_volume = deque(maxlen=max_points)  # Track volume separately
        self.put_volume = deque(maxlen=max_points)
        self.underlying_price = deque(maxlen=max_points)  # Track underlying price
        
        # Trading hours
        self.ET = pytz.timezone('US/Eastern')
        self.MARKET_OPEN = (9, 30)
        self.MARKET_CLOSE = (16, 0)
        self.EXTENDED_START = (4, 0)
        self.EXTENDED_END = (20, 0)
        
        # Performance tracking
        self.fetch_count = 0
        self.error_count = 0
        self.last_successful_fetch = None
        
    def is_trading_time(self) -> bool:
        """Check if current time is within extended trading hours"""
        now_et = datetime.now(self.ET)
        current_time = (now_et.hour, now_et.minute)
        
        # Weekend check
        if now_et.weekday() >= 5:
            return False
        
        # Extended hours check
        start_minutes = self.EXTENDED_START[0] * 60 + self.EXTENDED_START[1]
        end_minutes = self.EXTENDED_END[0] * 60 + self.EXTENDED_END[1]
        current_minutes = current_time[0] * 60 + current_time[1]
        
        return start_minutes <= current_minutes <= end_minutes

    def get_market_status(self) -> str:
        """Get detailed market status"""
        now_et = datetime.now(self.ET)
        current_time = (now_et.hour, now_et.minute)
        
        if now_et.weekday() >= 5:
            return "Market Closed (Weekend)"
        
        start_minutes = self.EXTENDED_START[0] * 60 + self.EXTENDED_START[1]
        end_minutes = self.EXTENDED_END[0] * 60 + self.EXTENDED_END[1]
        market_open_minutes = self.MARKET_OPEN[0] * 60 + self.MARKET_OPEN[1]
        market_close_minutes = self.MARKET_CLOSE[0] * 60 + self.MARKET_CLOSE[1]
        current_minutes = current_time[0] * 60 + current_time[1]
        
        if current_minutes < start_minutes or current_minutes > end_minutes:
            return "Market Closed"
        elif current_minutes < market_open_minutes:
            return "Pre-Market"
        elif current_minutes <= market_close_minutes:
            return "Regular Hours"
        else:
            return "After Hours"

    def calculate_option_metrics(self, options_data: Dict[str, Any]) -> Tuple[float, float, int, int, float]:
        """Calculate comprehensive option metrics"""
        call_delta_vol = 0.0
        put_delta_vol = 0.0
        call_vol = 0
        put_vol = 0
        underlying = 0.0

        if not options_data:
            return call_delta_vol, put_delta_vol, call_vol, put_vol, underlying

        # Get underlying price
        underlying = options_data.get("underlying", {}).get("mark", 0.0)
        
        # Process calls
        for expiry in options_data.get("callExpDateMap", {}):
            for strike in options_data["callExpDateMap"][expiry]:
                for option in options_data["callExpDateMap"][expiry][strike]:
                    delta = option.get("delta", 0.0)
                    volume = option.get("totalVolume", 0)
                    if volume > 0:
                        call_delta_vol += delta * volume
                        call_vol += volume

        # Process puts (delta is negative, so we take absolute value for put_delta_vol)
        for expiry in options_data.get("putExpDateMap", {}):
            for strike in options_data["putExpDateMap"][expiry]:
                for option in options_data["putExpDateMap"][expiry][strike]:
                    delta = option.get("delta", 0.0)
                    volume = option.get("totalVolume", 0)
                    if volume > 0:
                        put_delta_vol += abs(delta) * volume  # Use absolute value
                        put_vol += volume

        return call_delta_vol, put_delta_vol, call_vol, put_vol, underlying

    def fetch_data(self) -> Tuple[float, float, int, int, float]:
        """Fetch options data with enhanced error handling"""
        try:
            now = datetime.now(timezone.utc)
            from_date = now
            to_date = now + timedelta(days=7)
            
            options_data = self.client.get_Option(
                self.symbol, from_date, to_date, self.strike_count
            )
            
            metrics = self.calculate_option_metrics(options_data)
            self.fetch_count += 1
            self.last_successful_fetch = datetime.now(self.ET)
            
            logger.info(f"Data fetch #{self.fetch_count} successful")
            return metrics
            
        except Exception as e:
            self.error_count += 1
            logger.error(f"Data fetch error #{self.error_count}: {e}")
            
            # Return last known values if available
            if self.call_deltas and self.put_deltas:
                return (self.call_deltas[-1], self.put_deltas[-1], 
                       self.call_volume[-1] if self.call_volume else 0,
                       self.put_volume[-1] if self.put_volume else 0,
                       self.underlying_price[-1] if self.underlying_price else 0.0)
            return 0.0, 0.0, 0, 0, 0.0

    def calculate_technical_indicators(self) -> Dict[str, float]:
        """Calculate additional technical indicators"""
        indicators = {}
        
        if len(self.net_deltas) < 2:
            return indicators
        
        # Momentum (rate of change)
        if len(self.net_deltas) >= 5:
            recent_avg = np.mean(list(self.net_deltas)[-3:])
            earlier_avg = np.mean(list(self.net_deltas)[-6:-3])
            indicators['momentum'] = (recent_avg - earlier_avg) / abs(earlier_avg) if earlier_avg != 0 else 0
        
        # Volatility (standard deviation of net delta)
        if len(self.net_deltas) >= 10:
            indicators['volatility'] = np.std(list(self.net_deltas)[-10:])
        
        # Trend strength (correlation with time)
        if len(self.net_deltas) >= 10:
            time_series = np.arange(len(self.net_deltas))
            indicators['trend'] = np.corrcoef(time_series, list(self.net_deltas))[0, 1]
        
        return indicators

    def update_plot(self, frame):
        """Enhanced plot update with additional metrics"""
        try:
            market_status = self.get_market_status()
            
            # Fetch new data if trading
            if self.is_trading_time():
                call_delta_vol, put_delta_vol, call_vol, put_vol, underlying = self.fetch_data()
            else:
                # Use last values if market closed
                call_delta_vol = self.call_deltas[-1] if self.call_deltas else 0.0
                put_delta_vol = self.put_deltas[-1] if self.put_deltas else 0.0
                call_vol = self.call_volume[-1] if self.call_volume else 0
                put_vol = self.put_volume[-1] if self.put_volume else 0
                underlying = self.underlying_price[-1] if self.underlying_price else 0.0
            
            current_time = datetime.now(self.ET)
            
            # Calculate derived metrics
            net_delta = call_delta_vol - put_delta_vol
            ratio = call_delta_vol / put_delta_vol if put_delta_vol != 0 else float('inf')
            
            # Store data
            self.timestamps.append(current_time)
            self.call_deltas.append(call_delta_vol)
            self.put_deltas.append(put_delta_vol)
            self.net_deltas.append(net_delta)
            self.delta_ratio.append(ratio)
            self.call_volume.append(call_vol)
            self.put_volume.append(put_vol)
            self.underlying_price.append(underlying)
            
            # Calculate technical indicators
            indicators = self.calculate_technical_indicators()
            
            # Clear and setup subplots
            self.fig.clear()
            gs = self.fig.add_gridspec(3, 2, height_ratios=[2, 1, 1], hspace=0.4, wspace=0.3)
            
            if len(self.timestamps) > 1:
                time_nums = mdates.date2num(self.timestamps)
                
                # Main Delta×Volume plot
                ax1 = self.fig.add_subplot(gs[0, :])
                ax1.plot(time_nums, self.call_deltas, label="Call Δ×Vol", color="lime", 
                        linewidth=3, marker='o', markersize=3, alpha=0.9)
                ax1.plot(time_nums, self.put_deltas, label="Put Δ×Vol", color="red", 
                        linewidth=3, marker='s', markersize=3, alpha=0.9)
                
                # Add fill and zero line
                ax1.fill_between(time_nums, self.call_deltas, 0, alpha=0.2, color='lime')
                ax1.fill_between(time_nums, self.put_deltas, 0, alpha=0.2, color='red')
                ax1.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
                
                # Formatting
                ax1.set_title(f"Live Delta × Volume - {self.symbol} Options ({market_status})", 
                             fontsize=14, fontweight='bold')
                ax1.set_ylabel("Δ × Volume", fontsize=12)
                ax1.legend(loc="lower right")
                ax1.grid(True, alpha=0.3)
                
                # Net Delta plot
                ax2 = self.fig.add_subplot(gs[1, 0])
                ax2.plot(time_nums, self.net_deltas, color='purple', linewidth=2, marker='d', markersize=2)
                ax2.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
                ax2.fill_between(time_nums, self.net_deltas, 0, alpha=0.3, 
                               color='green' if self.net_deltas[-1] > 0 else 'red')
                ax2.set_title("Net Delta (Call - Put)", fontsize=11)
                ax2.set_ylabel("Net Δ×Vol", fontsize=10)
                ax2.grid(True, alpha=0.3)
                
                # Volume plot
                ax3 = self.fig.add_subplot(gs[1, 1])
                ax3.plot(time_nums, self.call_volume, color='lightgreen', linewidth=2, label='Call Vol')
                ax3.plot(time_nums, self.put_volume, color='lightcoral', linewidth=2, label='Put Vol')
                ax3.set_title("Option Volume", fontsize=11)
                ax3.set_ylabel("Volume", fontsize=10)
                ax3.legend(fontsize=8)
                ax3.grid(True, alpha=0.3)
                
                # Technical indicators plot
                ax4 = self.fig.add_subplot(gs[2, :])
                if 'momentum' in indicators:
                    ax4.text(0.02, 0.85, f"Momentum: {indicators['momentum']:.3f}", 
                            transform=ax4.transAxes, fontsize=10)
                if 'volatility' in indicators:
                    ax4.text(0.02, 0.65, f"Volatility: {indicators['volatility']:.0f}", 
                            transform=ax4.transAxes, fontsize=10)
                if 'trend' in indicators:
                    ax4.text(0.02, 0.45, f"Trend: {indicators['trend']:.3f}", 
                            transform=ax4.transAxes, fontsize=10)
                
                # Performance stats
                success_rate = ((self.fetch_count - self.error_count) / self.fetch_count * 100) if self.fetch_count > 0 else 0
                ax4.text(0.02, 0.25, f"Success Rate: {success_rate:.1f}%", 
                        transform=ax4.transAxes, fontsize=10)
                
                ax4.set_title("Technical Indicators & Performance", fontsize=11)
                ax4.set_xlim(0, 1)
                ax4.set_ylim(0, 1)
                ax4.axis('off')
                
                # Format time axis for all plots
                for ax in [ax1, ax2, ax3]:
                    ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
                    ax.xaxis.set_major_locator(mdates.MinuteLocator(interval=max(1, len(self.timestamps)//8)))
                    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right', fontsize=8)
                
                # Enhanced info box
                sentiment = "Bullish 🟢" if net_delta > 0 else "Bearish 🔴"
                latest_time = current_time.strftime('%H:%M:%S ET')
                
                info_text = f'{sentiment}\nTime: {latest_time}\nStatus: {market_status}\n'
                info_text += f'Underlying: ${underlying:.2f}\n'
                info_text += f'Call Δ×Vol: {call_delta_vol:,.0f}\nPut Δ×Vol: {put_delta_vol:,.0f}\n'
                info_text += f'Net: {net_delta:,.0f}\nRatio: {ratio:.2f}\n'
                info_text += f'Fetches: {self.fetch_count} | Errors: {self.error_count}'
                
                ax1.text(0.02, 0.98, info_text, transform=ax1.transAxes, 
                        verticalalignment='top', fontsize=9,
                        bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.9))
            
        except Exception as e:
            logger.error(f"Plot update error: {e}")
            self.fig.clear()
            ax = self.fig.add_subplot(111)
            ax.text(0.5, 0.5, f'Error: {str(e)}', transform=ax.transAxes, 
                   ha='center', va='center', fontsize=12, color='red')

    def run(self, update_interval: int = 30000):
        """Start the monitoring dashboard"""
        plt.style.use('default')
        self.fig, _ = plt.subplots(figsize=(14, 8))
        self.fig.canvas.mpl_connect('close_event', self._on_close)
        
        # Start animation
        self.ani = FuncAnimation(self.fig, self.update_plot, 
                               interval=update_interval, repeat=True, 
                               cache_frame_data=False)
        
        logger.info(f"Starting monitor for {self.symbol} options...")
        logger.info(f"Update interval: {update_interval/1000} seconds")
        logger.info(f"Current status: {self.get_market_status()}")
        
        # Initial data fetch
        self.update_plot(0)
        plt.show()
        
    def _on_close(self, event):
        """Handle window close"""
        logger.info("Monitor stopped by user")

# Usage
if __name__ == "__main__":
    try:
        # Create client and run setup if needed
        from schwab_api import Client
        client = Client()
        
        # Check if setup is needed
        if not client.check_session():
            print("Running Schwab API setup...")
            if client.setup():
                print("Setup completed successfully!")
            else:
                print("Setup failed. Please check your credentials and try again.")
                exit(1)
        
        # Now create the monitor
        monitor = OptionsMonitor(symbol="SPY", strike_count=20, max_points=100)
        monitor.run(update_interval=30000)  # 30 seconds
    except Exception as e:
        print(f"Error initializing application: {e}")
